<?php
/**
 * The file that defines the core plugin class
 *
 * A class definition that includes attributes and functions used across both the
 * public-facing side of the site and the admin area.
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/includes
 */

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * Also maintains the unique identifier of this plugin as well as the current
 * version of the plugin.
 */
class Houzez_Ads_Extension {

	/**
	 * The loader that's responsible for maintaining and registering all hooks that power
	 * the plugin.
	 *
	 * @var Houzez_Ads_Loader $loader Maintains and registers all hooks for the plugin.
	 */
	protected $loader;

	/**
	 * The unique identifier of this plugin.
	 *
	 * @var string $plugin_name The string used to uniquely identify this plugin.
	 */
	protected $plugin_name;

	/**
	 * The current version of the plugin.
	 *
	 * @var string $version The current version of the plugin.
	 */
	protected $version;

	/**
	 * Define the core functionality of the plugin.
	 *
	 * Set the plugin name and the plugin version that can be used throughout the plugin.
	 * Load the dependencies, define the locale, and set the hooks for the admin area and
	 * the public-facing side of the site.
	 */
	public function __construct() {
		if ( defined( 'HOUZEZ_ADS_EXTENSION_VERSION' ) ) {
			$this->version = HOUZEZ_ADS_EXTENSION_VERSION;
		} else {
			$this->version = '1.0.0';
		}
		$this->plugin_name = 'houzez-ads-extension';

		$this->load_dependencies();
		$this->set_locale();
		$this->define_admin_hooks();
		$this->define_public_hooks();
		$this->init_dashboard_integration();
	}

	/**
	 * Load the required dependencies for this plugin.
	 */
	private function load_dependencies() {
		/**
		 * The class responsible for orchestrating the actions and filters of the
		 * core plugin.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-ads-loader.php';

		/**
		 * The class responsible for defining internationalization functionality
		 * of the plugin.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-ads-i18n.php';

		/**
		 * The class responsible for defining all actions that occur in the admin area.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'admin/class-houzez-ads-admin.php';

		/**
		 * The class responsible for defining all actions that occur in the public-facing
		 * side of the site.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'frontend/class-houzez-ads-frontend.php';

		/**
		 * The class responsible for WooCommerce integration.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'woocommerce/class-houzez-ads-woocommerce.php';

		/**
		 * The campaign model class.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'models/class-houzez-banner-campaign.php';

		/**
		 * Helper functions and utilities.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/helpers.php';
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/pricing.php';
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/targeting.php';

		/**
		 * Dashboard integration class.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-dashboard-integration.php';

		$this->loader = new Houzez_Ads_Loader();
	}

	/**
	 * Define the locale for this plugin for internationalization.
	 */
	private function set_locale() {
		$plugin_i18n = new Houzez_Ads_i18n();
		$this->loader->add_action( 'plugins_loaded', $plugin_i18n, 'load_plugin_textdomain' );
	}

	/**
	 * Register all of the hooks related to the admin area functionality
	 * of the plugin.
	 */
	private function define_admin_hooks() {
		$plugin_admin = new Houzez_Ads_Admin( $this->get_plugin_name(), $this->get_version() );

		$this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_styles' );
		$this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts' );
		$this->loader->add_action( 'init', $this, 'register_campaign_post_type' );
		$this->loader->add_action( 'add_meta_boxes', $plugin_admin, 'add_campaign_meta_boxes' );
		$this->loader->add_action( 'save_post', $plugin_admin, 'save_campaign_meta' );

		// Admin list table customizations
		$this->loader->add_filter( 'manage_banner_campaign_posts_columns', $plugin_admin, 'add_campaign_columns' );
		$this->loader->add_action( 'manage_banner_campaign_posts_custom_column', $plugin_admin, 'display_campaign_columns', 10, 2 );
		$this->loader->add_filter( 'manage_edit-banner_campaign_sortable_columns', $plugin_admin, 'add_sortable_columns' );
		$this->loader->add_action( 'restrict_manage_posts', $plugin_admin, 'add_campaign_filters' );
		$this->loader->add_action( 'parse_query', $plugin_admin, 'filter_campaigns_by_meta' );
		$this->loader->add_filter( 'bulk_actions-edit-banner_campaign', $plugin_admin, 'add_bulk_actions' );
		$this->loader->add_filter( 'handle_bulk_actions-edit-banner_campaign', $plugin_admin, 'handle_bulk_actions', 10, 3 );

		// Admin AJAX handlers
		$this->loader->add_action( 'wp_ajax_houzez_ads_get_pricing', $plugin_admin, 'handle_get_pricing' );
	}

	/**
	 * Register the `banner_campaign` custom post type.
	 */
	public function register_campaign_post_type() {
		$labels = array(
			'name'                  => _x( 'Campaigns', 'Post type general name', 'houzez-ads-extension' ),
			'singular_name'         => _x( 'Campaign', 'Post type singular name', 'houzez-ads-extension' ),
			'menu_name'             => _x( 'Ad Campaigns', 'Admin Menu text', 'houzez-ads-extension' ),
			'name_admin_bar'        => _x( 'Campaign', 'Add New on Toolbar', 'houzez-ads-extension' ),
			'add_new'               => __( 'Add New', 'houzez-ads-extension' ),
			'add_new_item'          => __( 'Add New Campaign', 'houzez-ads-extension' ),
			'new_item'              => __( 'New Campaign', 'houzez-ads-extension' ),
			'edit_item'             => __( 'Edit Campaign', 'houzez-ads-extension' ),
			'view_item'             => __( 'View Campaign', 'houzez-ads-extension' ),
			'all_items'             => __( 'All Campaigns', 'houzez-ads-extension' ),
			'search_items'          => __( 'Search Campaigns', 'houzez-ads-extension' ),
			'parent_item_colon'     => __( 'Parent Campaigns:', 'houzez-ads-extension' ),
			'not_found'             => __( 'No campaigns found.', 'houzez-ads-extension' ),
			'not_found_in_trash'    => __( 'No campaigns found in Trash.', 'houzez-ads-extension' ),
			'featured_image'        => _x( 'Campaign Cover Image', 'Overrides the “Featured Image” phrase for this post type. Added in 4.3', 'houzez-ads-extension' ),
			'set_featured_image'    => _x( 'Set cover image', 'Overrides the “Set featured image” phrase for this post type. Added in 4.3', 'houzez-ads-extension' ),
			'remove_featured_image' => _x( 'Remove cover image', 'Overrides the “Remove featured image” phrase for this post type. Added in 4.3', 'houzez-ads-extension' ),
			'use_featured_image'    => _x( 'Use as cover image', 'Overrides the “Use as featured image” phrase for this post type. Added in 4.3', 'houzez-ads-extension' ),
			'archives'              => _x( 'Campaign archives', 'The post type archive label used in nav menus. Default “Post Archives”. Added in 4.4', 'houzez-ads-extension' ),
			'insert_into_item'      => _x( 'Insert into campaign', 'Overrides the “Insert into post”/”Insert into page” phrase (used when inserting media into a post). Added in 4.4', 'houzez-ads-extension' ),
			'uploaded_to_this_item' => _x( 'Uploaded to this campaign', 'Overrides the “Uploaded to this post”/”Uploaded to this page” phrase (used when viewing media attached to a post). Added in 4.4', 'houzez-ads-extension' ),
			'filter_items_list'     => _x( 'Filter campaigns list', 'Screen reader text for the filter links heading on the post type listing screen. Default “Filter posts list”/”Filter pages list”. Added in 4.4', 'houzez-ads-extension' ),
			'items_list_navigation' => _x( 'Campaigns list navigation', 'Screen reader text for the pagination heading on the post type listing screen. Default “Posts list navigation”/”Pages list navigation”. Added in 4.4', 'houzez-ads-extension' ),
			'items_list'            => _x( 'Campaigns list', 'Screen reader text for the items list heading on the post type listing screen. Default “Posts list”/”Pages list”. Added in 4.4', 'houzez-ads-extension' ),
		);

		$args = array(
			'labels'             => $labels,
			'public'             => true,
			'publicly_queryable' => true,
			'show_ui'            => true,
			'show_in_menu'       => true,
			'query_var'          => true,
			'rewrite'            => array( 'slug' => 'ad-campaign' ),
			'capability_type'    => 'post',
			'has_archive'        => true,
			'hierarchical'       => false,
			'menu_position'      => null,
			'supports'           => array( 'title', 'editor', 'author', 'thumbnail' ),
			'show_in_rest'       => true,
		);

		register_post_type( 'banner_campaign', $args );
	}

	/**
	 * Register all of the hooks related to the public-facing functionality
	 * of the plugin.
	 */
	private function define_public_hooks() {
		$plugin_public = new Houzez_Ads_Frontend( $this->get_plugin_name(), $this->get_version() );

		$this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_styles' );
		$this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_scripts' );
		$this->loader->add_action( 'init', $plugin_public, 'register_shortcodes' );
		
		// WooCommerce integration (only if WooCommerce is active)
		if ( class_exists( 'WooCommerce' ) ) {
			$plugin_woocommerce = new Houzez_Ads_WooCommerce( $this->get_plugin_name(), $this->get_version() );
			$this->loader->add_action( 'init', $plugin_woocommerce, 'init_woocommerce_integration' );
		}
	}

	/**
	 * Initialize dashboard integration.
	 */
	private function init_dashboard_integration() {
		new Houzez_Dashboard_Integration();
	}

	/**
	 * Run the loader to execute all of the hooks with WordPress.
	 */
	public function run() {
		$this->loader->run();
	}

	/**
	 * The name of the plugin used to uniquely identify it within the context of
	 * WordPress and to define internationalization functionality.
	 *
	 * @return string The name of the plugin.
	 */
	public function get_plugin_name() {
		return $this->plugin_name;
	}

	/**
	 * The reference to the class that orchestrates the hooks with the plugin.
	 *
	 * @return Houzez_Ads_Loader Orchestrates the hooks of the plugin.
	 */
	public function get_loader() {
		return $this->loader;
	}

	/**
	 * Retrieve the version number of the plugin.
	 *
	 * @return string The version number of the plugin.
	 */
	public function get_version() {
		return $this->version;
	}
}
