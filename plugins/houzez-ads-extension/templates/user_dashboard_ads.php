<?php
/**
 * Template Name: Ads Dashboard
 * 
 * Main dashboard page template for Houzez Ads Extension
 * Replicates the exact structure of Houzez user_dashboard.php
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Check if user is logged in
if ( ! is_user_logged_in() ) {
    wp_redirect( wp_login_url( get_permalink() ) );
    exit;
}

// Check user permissions
if ( ! houzez_ads_user_can_create_campaigns() ) {
    wp_redirect( home_url() );
    exit;
}

global $houzez_local;

// Get current user data
$userID = get_current_user_id();
$user_credits = houzez_ads_get_user_credits( $userID );
$campaign_counts = houzez_ads_get_user_campaign_counts( $userID );

// Determine which section to show
$ads_page = isset( $_GET['ads_page'] ) ? sanitize_text_field( $_GET['ads_page'] ) : 'overview';

// Handle credit check for campaign creation
if ( $ads_page === 'create_campaign' && $user_credits <= 0 ) {
    wp_redirect( add_query_arg( array( 'ads_page' => 'credits', 'notice' => 'insufficient_credits' ), get_permalink() ) );
    exit;
}

get_header('dashboard'); ?>

<div class="dashboard-wrap">
    <div class="dashboard-nav">
        <?php get_template_part('template-parts/dashboard/sidebar'); ?>
    </div>
    
    <div class="dashboard-content">
        <?php get_template_part('template-parts/dashboard/topbar'); ?>
        
        <div class="dashboard-content-inner">
            <?php
            // Load appropriate ads dashboard section
            switch ( $ads_page ) {
                case 'overview':
                case 'dashboard':
                    houzez_ads_get_template_part( 'template-parts/ads-dashboard/overview' );
                    break;
                    
                case 'campaigns':
                    houzez_ads_get_template_part( 'template-parts/ads-dashboard/campaigns/main' );
                    break;
                    
                case 'create_campaign':
                    houzez_ads_get_template_part( 'template-parts/ads-dashboard/campaigns/create' );
                    break;
                    
                case 'insights':
                    houzez_ads_get_template_part( 'template-parts/ads-dashboard/insights/main' );
                    break;
                    
                case 'credits':
                    houzez_ads_get_template_part( 'template-parts/ads-dashboard/credits/main' );
                    break;
                    
                default:
                    houzez_ads_get_template_part( 'template-parts/ads-dashboard/overview' );
                    break;
            }
            ?>
        </div>
        
        <?php get_template_part('template-parts/dashboard/dashboard-footer'); ?>
    </div>
</div>

<?php get_footer('dashboard'); ?>

<script>
jQuery(document).ready(function($) {
    // Initialize ads dashboard functionality
    if (typeof houzezAdsDashboard !== 'undefined') {
        houzezAdsDashboard.init();
    }
    
    // Handle credit check notifications
    <?php if ( isset( $_GET['notice'] ) && $_GET['notice'] === 'insufficient_credits' ) : ?>
        $('.dashboard-content-inner').prepend(
            '<div class="alert alert-warning alert-dismissible fade show" role="alert">' +
                '<strong><?php _e( 'Insufficient Credits', 'houzez-ads-extension' ); ?></strong> ' +
                '<?php _e( 'You need to purchase credits before creating a campaign.', 'houzez-ads-extension' ); ?>' +
                '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
            '</div>'
        );
    <?php endif; ?>
});
</script>
