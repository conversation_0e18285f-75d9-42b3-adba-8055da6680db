<?php
/**
 * The admin-specific functionality of the plugin
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for admin-specific functionality.
 */
class Houzez_Ads_Admin {

	/**
	 * The ID of this plugin.
	 *
	 * @var string $plugin_name The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @var string $version The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @param string $plugin_name The name of this plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;

		// Add admin menu
		add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
		add_action( 'admin_init', array( $this, 'register_settings' ) );
	}



	/**
	 * Register the stylesheets for the admin area.
	 */
	public function enqueue_styles() {
		wp_enqueue_style( 
			$this->plugin_name, 
			HOUZEZ_ADS_EXTENSION_URL . 'admin/css/houzez-ads-admin.css', 
			array(), 
			$this->version, 
			'all' 
		);
	}

	/**
	 * Register the JavaScript for the admin area.
	 */
	public function enqueue_scripts() {
		wp_enqueue_script(
			$this->plugin_name,
			HOUZEZ_ADS_EXTENSION_URL . 'admin/js/houzez-ads-admin.js',
			array( 'jquery' ),
			$this->version,
			false
		);

		// Localize script for AJAX
		wp_localize_script( $this->plugin_name, 'houzez_ads_admin', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce( 'houzez_ads_admin_nonce' )
		));
	}

	/**
	 * Add meta boxes for campaign post type.
	 */
	public function add_campaign_meta_boxes() {
		add_meta_box(
			'houzez_campaign_details',
			__( 'Campaign Details', 'houzez-ads-extension' ),
			array( $this, 'campaign_details_meta_box' ),
			'banner_campaign',
			'normal',
			'high'
		);

		add_meta_box(
			'houzez_campaign_banner',
			__( 'Banner Settings', 'houzez-ads-extension' ),
			array( $this, 'campaign_banner_meta_box' ),
			'banner_campaign',
			'normal',
			'high'
		);

		add_meta_box(
			'houzez_campaign_analytics',
			__( 'Campaign Analytics', 'houzez-ads-extension' ),
			array( $this, 'campaign_analytics_meta_box' ),
			'banner_campaign',
			'side',
			'default'
		);
	}

	/**
	 * Campaign details meta box callback.
	 *
	 * @param WP_Post $post Current post object.
	 */
	public function campaign_details_meta_box( $post ) {
		wp_nonce_field( 'houzez_campaign_meta_box', 'houzez_campaign_meta_box_nonce' );

		$campaign = new Houzez_Banner_Campaign( $post );
		$user_type = houzez_ads_get_user_type( $post->post_author );
		$ad_types = houzez_ads_get_available_ad_types( $post->post_author );
		$statuses = houzez_ads_get_campaign_statuses();
		$user_properties = houzez_ads_get_user_properties( $post->post_author );
		$ad_zones = houzez_ads_get_available_zones_for_user( $campaign->ad_type, $post->post_author );

		include HOUZEZ_ADS_EXTENSION_PATH . 'admin/partials/campaign-details-meta-box.php';
	}

	/**
	 * Campaign banner meta box callback.
	 *
	 * @param WP_Post $post Current post object.
	 */
	public function campaign_banner_meta_box( $post ) {
		$campaign = new Houzez_Banner_Campaign( $post );
		include HOUZEZ_ADS_EXTENSION_PATH . 'admin/partials/campaign-banner-meta-box.php';
	}

	/**
	 * Campaign analytics meta box callback.
	 *
	 * @param WP_Post $post Current post object.
	 */
	public function campaign_analytics_meta_box( $post ) {
		$campaign = new Houzez_Banner_Campaign( $post );
		$analytics = $campaign->get_analytics();
		include HOUZEZ_ADS_EXTENSION_PATH . 'admin/partials/campaign-analytics-meta-box.php';
	}

	/**
	 * Save campaign meta data.
	 *
	 * @param int $post_id Post ID.
	 */
	public function save_campaign_meta( $post_id ) {
		// Prevent infinite loops
		static $saving = false;
		if ( $saving ) {
			return;
		}

		// Check if this is the right post type
		if ( get_post_type( $post_id ) !== 'banner_campaign' ) {
			return;
		}

		// Check if our nonce is set
		if ( ! isset( $_POST['houzez_campaign_meta_box_nonce'] ) ) {
			return;
		}

		// Verify that the nonce is valid
		if ( ! wp_verify_nonce( $_POST['houzez_campaign_meta_box_nonce'], 'houzez_campaign_meta_box' ) ) {
			return;
		}

		// If this is an autosave, our form has not been submitted, so we don't want to do anything
		if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
			return;
		}

		// Check the user's permissions
		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			return;
		}

		// Set saving flag to prevent infinite loops
		$saving = true;

		// Sanitize and save the data
		$data = array();

		if ( isset( $_POST['houzez_ad_type'] ) ) {
			$data['ad_type'] = sanitize_text_field( $_POST['houzez_ad_type'] );
		}

		if ( isset( $_POST['houzez_ad_zone'] ) ) {
			$data['ad_zone'] = sanitize_text_field( $_POST['houzez_ad_zone'] );
		}

		if ( isset( $_POST['houzez_duration'] ) ) {
			$data['duration'] = absint( $_POST['houzez_duration'] );
		}

		if ( isset( $_POST['houzez_selected_properties'] ) ) {
			$data['selected_properties'] = array_map( 'absint', $_POST['houzez_selected_properties'] );
		}

		if ( isset( $_POST['houzez_banner_image'] ) ) {
			$data['banner_image'] = esc_url_raw( $_POST['houzez_banner_image'] );
		}

		if ( isset( $_POST['houzez_banner_link'] ) ) {
			$data['banner_link'] = esc_url_raw( $_POST['houzez_banner_link'] );
		}

		if ( isset( $_POST['houzez_banner_alt'] ) ) {
			$data['banner_alt'] = sanitize_text_field( $_POST['houzez_banner_alt'] );
		}

		if ( isset( $_POST['houzez_campaign_status'] ) ) {
			$data['campaign_status'] = sanitize_text_field( $_POST['houzez_campaign_status'] );
		}

		// Handle custom dates
		if ( isset( $_POST['use_custom_dates'] ) && $_POST['use_custom_dates'] ) {
			if ( isset( $_POST['houzez_start_date'] ) && ! empty( $_POST['houzez_start_date'] ) ) {
				$data['start_date'] = sanitize_text_field( $_POST['houzez_start_date'] );
			}
			if ( isset( $_POST['houzez_end_date'] ) && ! empty( $_POST['houzez_end_date'] ) ) {
				$data['end_date'] = sanitize_text_field( $_POST['houzez_end_date'] );
			}
		}

		// Calculate and save price
		if ( ! empty( $data['ad_zone'] ) && ! empty( $data['duration'] ) ) {
			$quantity = 1;
			if ( isset( $data['selected_properties'] ) && is_array( $data['selected_properties'] ) ) {
				$quantity = count( $data['selected_properties'] );
			}
			$data['price'] = houzez_ads_calculate_campaign_price(
				$data['ad_zone'],
				$data['duration'],
				$quantity,
				$data['ad_type'] ?? 'property'
			);
		}

		// Handle admin-specific fields
		if ( current_user_can( 'manage_options' ) ) {
			// Handle admin notes
			if ( isset( $_POST['admin_notes'] ) ) {
				update_post_meta( $post_id, '_houzez_admin_notes', sanitize_textarea_field( $_POST['admin_notes'] ) );
			}

			// Handle user type override
			if ( isset( $_POST['user_type_override'] ) && ! empty( $_POST['user_type_override'] ) ) {
				update_post_meta( $post_id, '_houzez_user_type_override', sanitize_text_field( $_POST['user_type_override'] ) );
			}

			// Handle post author override for new campaigns
			if ( isset( $_POST['post_author_override'] ) && ! empty( $_POST['post_author_override'] ) ) {
				$new_author_id = absint( $_POST['post_author_override'] );
				if ( $new_author_id && get_userdata( $new_author_id ) ) {
					// Update post author
					wp_update_post( array(
						'ID' => $post_id,
						'post_author' => $new_author_id
					) );
				}
			}

			// For admin-created campaigns, set default status and skip payment
			if ( ! isset( $data['campaign_status'] ) ) {
				$data['campaign_status'] = 'approved'; // Auto-approve admin campaigns
			}
		}

		// Save meta data directly to avoid infinite loop
		foreach ( $data as $key => $value ) {
			$meta_key = '_houzez_' . $key;
			if ( $key === 'campaign_status' ) {
				$meta_key = '_houzez_campaign_status';
			} elseif ( $key === 'price' ) {
				$meta_key = '_houzez_campaign_price';
			}
			update_post_meta( $post_id, $meta_key, $value );
		}

		// Reset saving flag
		$saving = false;
	}

	/**
	 * Add custom columns to campaign list table.
	 *
	 * @param array $columns Existing columns.
	 * @return array Modified columns.
	 */
	public function add_campaign_columns( $columns ) {
		$new_columns = array();

		// Keep checkbox and title
		$new_columns['cb'] = $columns['cb'];
		$new_columns['title'] = $columns['title'];

		// Add custom columns
		$new_columns['banner_preview'] = __( 'Banner', 'houzez-ads-extension' );
		$new_columns['ad_type'] = __( 'Type', 'houzez-ads-extension' );
		$new_columns['ad_zone'] = __( 'Zone', 'houzez-ads-extension' );
		$new_columns['duration'] = __( 'Duration', 'houzez-ads-extension' );
		$new_columns['campaign_status'] = __( 'Status', 'houzez-ads-extension' );
		$new_columns['analytics'] = __( 'Analytics', 'houzez-ads-extension' );
		$new_columns['price'] = __( 'Price', 'houzez-ads-extension' );

		// Keep author and date
		$new_columns['author'] = $columns['author'];
		$new_columns['date'] = $columns['date'];

		return $new_columns;
	}

	/**
	 * Display custom column content.
	 *
	 * @param string $column  Column name.
	 * @param int    $post_id Post ID.
	 */
	public function display_campaign_columns( $column, $post_id ) {
		$campaign = new Houzez_Banner_Campaign( $post_id );

		switch ( $column ) {
			case 'banner_preview':
				if ( $campaign->banner_image ) {
					echo '<img src="' . esc_url( $campaign->banner_image ) . '" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;" />';
				} else {
					echo '<span class="dashicons dashicons-format-image" style="color: #ddd; font-size: 40px;"></span>';
				}
				break;

			case 'ad_type':
				$ad_types = houzez_ads_get_available_ad_types();
				echo esc_html( $ad_types[ $campaign->ad_type ] ?? $campaign->ad_type );
				break;

			case 'ad_zone':
				$ad_zones = houzez_ads_get_available_zones();
				echo esc_html( $ad_zones[ $campaign->ad_zone ] ?? $campaign->ad_zone );
				break;

			case 'duration':
				if ( $campaign->duration ) {
					printf( _n( '%d day', '%d days', $campaign->duration, 'houzez-ads-extension' ), $campaign->duration );

					if ( $campaign->end_date ) {
						$days_remaining = ceil( ( strtotime( $campaign->end_date ) - current_time( 'timestamp' ) ) / ( 24 * 60 * 60 ) );
						if ( $days_remaining > 0 ) {
							echo '<br><small style="color: #666;">' . sprintf( __( '%d days left', 'houzez-ads-extension' ), $days_remaining ) . '</small>';
						} else {
							echo '<br><small style="color: #d63638;">' . __( 'Expired', 'houzez-ads-extension' ) . '</small>';
						}
					}
				}
				break;

			case 'campaign_status':
				$statuses = houzez_ads_get_campaign_statuses();
				$status = $campaign->campaign_status;
				$status_label = $statuses[ $status ] ?? $status;

				$status_colors = array(
					'pending' => '#f0ad4e',
					'approved' => '#5cb85c',
					'rejected' => '#d9534f',
					'expired' => '#6c757d',
					'paused' => '#17a2b8'
				);

				$color = $status_colors[ $status ] ?? '#6c757d';
				echo '<span style="background: ' . esc_attr( $color ) . '; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600;">' . esc_html( $status_label ) . '</span>';
				break;

			case 'analytics':
				if ( $campaign->campaign_status === 'approved' ) {
					$analytics = $campaign->get_analytics();
					echo '<div style="font-size: 12px;">';
					echo '<div>' . sprintf( __( 'Views: %s', 'houzez-ads-extension' ), number_format( $analytics['impressions'] ) ) . '</div>';
					echo '<div>' . sprintf( __( 'Clicks: %s', 'houzez-ads-extension' ), number_format( $analytics['clicks'] ) ) . '</div>';
					echo '<div>' . sprintf( __( 'CTR: %s%%', 'houzez-ads-extension' ), $analytics['ctr'] ) . '</div>';
					echo '</div>';
				} else {
					echo '<span style="color: #999;">—</span>';
				}
				break;

			case 'price':
				if ( $campaign->price ) {
					echo houzez_ads_format_price( $campaign->price );
				} else {
					echo '<span style="color: #999;">—</span>';
				}
				break;
		}
	}

/**
 * Add sortable columns.
 *
 * @param array $columns Sortable columns.
 * @return array Modified sortable columns.
 */
public function add_sortable_columns( $columns ) {
	$columns['ad_type'] = 'ad_type';
	$columns['ad_zone'] = 'ad_zone';
	$columns['duration'] = 'duration';
	$columns['campaign_status'] = 'campaign_status';
	$columns['price'] = 'price';

	return $columns;
}

/**
 * Add campaign filters to admin list.
 */
public function add_campaign_filters() {
	global $typenow;

	if ( $typenow !== 'banner_campaign' ) {
		return;
	}

	// Status filter
	$statuses = houzez_ads_get_campaign_statuses();
	$current_status = $_GET['campaign_status'] ?? '';

	echo '<select name="campaign_status">';
	echo '<option value="">' . __( 'All Statuses', 'houzez-ads-extension' ) . '</option>';
	foreach ( $statuses as $key => $label ) {
		$selected = selected( $current_status, $key, false );
		echo '<option value="' . esc_attr( $key ) . '"' . $selected . '>' . esc_html( $label ) . '</option>';
	}
	echo '</select>';

	// Zone filter
	$zones = houzez_ads_get_available_zones();
	$current_zone = $_GET['ad_zone'] ?? '';

	echo '<select name="ad_zone">';
	echo '<option value="">' . __( 'All Zones', 'houzez-ads-extension' ) . '</option>';
	foreach ( $zones as $key => $label ) {
		$selected = selected( $current_zone, $key, false );
		echo '<option value="' . esc_attr( $key ) . '"' . $selected . '>' . esc_html( $label ) . '</option>';
	}
	echo '</select>';

	// Type filter
	$types = houzez_ads_get_available_ad_types();
	$current_type = $_GET['ad_type'] ?? '';

	echo '<select name="ad_type">';
	echo '<option value="">' . __( 'All Types', 'houzez-ads-extension' ) . '</option>';
	foreach ( $types as $key => $label ) {
		$selected = selected( $current_type, $key, false );
		echo '<option value="' . esc_attr( $key ) . '"' . $selected . '>' . esc_html( $label ) . '</option>';
	}
	echo '</select>';
}

/**
 * Filter campaigns by meta values.
 *
 * @param WP_Query $query The WP_Query object.
 */
public function filter_campaigns_by_meta( $query ) {
	global $pagenow, $typenow;

	if ( $pagenow !== 'edit.php' || $typenow !== 'banner_campaign' || ! is_admin() ) {
		return;
	}

	$meta_query = array();

	// Filter by status
	if ( ! empty( $_GET['campaign_status'] ) ) {
		$meta_query[] = array(
			'key' => '_houzez_campaign_status',
			'value' => sanitize_text_field( $_GET['campaign_status'] ),
			'compare' => '='
		);
	}

	// Filter by zone
	if ( ! empty( $_GET['ad_zone'] ) ) {
		$meta_query[] = array(
			'key' => '_houzez_ad_zone',
			'value' => sanitize_text_field( $_GET['ad_zone'] ),
			'compare' => '='
		);
	}

	// Filter by type
	if ( ! empty( $_GET['ad_type'] ) ) {
		$meta_query[] = array(
			'key' => '_houzez_ad_type',
			'value' => sanitize_text_field( $_GET['ad_type'] ),
			'compare' => '='
		);
	}

	if ( ! empty( $meta_query ) ) {
		$query->set( 'meta_query', $meta_query );
	}

	// Handle sorting
	$orderby = $query->get( 'orderby' );

	if ( in_array( $orderby, array( 'ad_type', 'ad_zone', 'duration', 'campaign_status', 'price' ) ) ) {
		$meta_key = '_houzez_' . $orderby;
		if ( $orderby === 'campaign_status' ) {
			$meta_key = '_houzez_campaign_status';
		} elseif ( $orderby === 'price' ) {
			$meta_key = '_houzez_campaign_price';
		}

		$query->set( 'meta_key', $meta_key );
		$query->set( 'orderby', 'meta_value' );
	}
}

/**
 * Add bulk actions.
 *
 * @param array $actions Existing bulk actions.
 * @return array Modified bulk actions.
 */
public function add_bulk_actions( $actions ) {
	$actions['approve_campaigns'] = __( 'Approve Campaigns', 'houzez-ads-extension' );
	$actions['reject_campaigns'] = __( 'Reject Campaigns', 'houzez-ads-extension' );
	$actions['pause_campaigns'] = __( 'Pause Campaigns', 'houzez-ads-extension' );

	return $actions;
}

/**
 * Handle bulk actions.
 *
 * @param string $redirect_to Redirect URL.
 * @param string $doaction    Action name.
 * @param array  $post_ids    Post IDs.
 * @return string Modified redirect URL.
 */
public function handle_bulk_actions( $redirect_to, $doaction, $post_ids ) {
	if ( ! in_array( $doaction, array( 'approve_campaigns', 'reject_campaigns', 'pause_campaigns' ) ) ) {
		return $redirect_to;
	}

	$processed = 0;

	foreach ( $post_ids as $post_id ) {
		$campaign = new Houzez_Banner_Campaign( $post_id );

		switch ( $doaction ) {
			case 'approve_campaigns':
				$campaign->approve();
				$processed++;
				break;

			case 'reject_campaigns':
				$campaign->reject();
				$processed++;
				break;

			case 'pause_campaigns':
				$campaign->save( array( 'campaign_status' => 'paused' ) );
				$processed++;
				break;
		}
	}

	$redirect_to = add_query_arg( array(
		'bulk_action' => $doaction,
		'processed' => $processed
	), $redirect_to );

	return $redirect_to;
}

/**
 * Handle get pricing AJAX request for admin.
 */
public function handle_get_pricing() {
	check_ajax_referer( 'houzez_ads_admin_nonce', 'nonce' );

	$ad_zone = sanitize_text_field( $_POST['ad_zone'] ?? '' );
	$duration = absint( $_POST['duration'] ?? 0 );
	$quantity = absint( $_POST['quantity'] ?? 1 );
	$ad_type = sanitize_text_field( $_POST['ad_type'] ?? 'property' );

	if ( $ad_zone && $duration ) {
		$price = houzez_ads_calculate_campaign_price( $ad_zone, $duration, $quantity, $ad_type );
		wp_send_json_success( array(
			'price' => $price,
			'formatted_price' => houzez_ads_format_price( $price ),
			'debug' => array(
				'zone' => $ad_zone,
				'duration' => $duration,
				'quantity' => $quantity,
				'type' => $ad_type
			)
		) );
	} else {
		wp_send_json_error( array(
			'message' => 'Invalid parameters',
			'debug' => array(
				'zone' => $ad_zone,
				'duration' => $duration,
				'quantity' => $quantity,
				'type' => $ad_type
			)
		) );
	}
}

/**
 * Add admin menu for plugin settings.
 */
public function add_admin_menu() {
		add_submenu_page(
			'edit.php?post_type=banner_campaign',
			__( 'Ad Campaign Settings', 'houzez-ads-extension' ),
			__( 'Settings', 'houzez-ads-extension' ),
			'manage_options',
			'houzez-ads-settings',
			array( $this, 'settings_page' )
		);

		add_submenu_page(
			'edit.php?post_type=banner_campaign',
			__( 'Credit Management', 'houzez-ads-extension' ),
			__( 'Credits', 'houzez-ads-extension' ),
			'manage_options',
			'houzez-ads-credits',
			array( $this, 'credits_page' )
		);
	}

	/**
	 * Register plugin settings.
	 */
	public function register_settings() {
		register_setting( 'houzez_ads_settings', 'houzez_ads_allowed_roles' );
		register_setting( 'houzez_ads_settings', 'houzez_ads_auto_approval' );
		register_setting( 'houzez_ads_settings', 'houzez_ads_credit_pricing' );

		// Handle credit management actions
		add_action( 'wp_ajax_houzez_ads_add_credits', array( $this, 'handle_add_credits' ) );
		add_action( 'wp_ajax_houzez_ads_assign_dummy_credits', array( $this, 'handle_assign_dummy_credits' ) );

		// Handle admin campaign creation actions
		add_action( 'wp_ajax_houzez_ads_get_user_info', array( $this, 'handle_get_user_info' ) );
		add_action( 'wp_ajax_houzez_ads_search_users', array( $this, 'handle_search_users' ) );
		add_action( 'wp_ajax_houzez_ads_get_user_form_data', array( $this, 'handle_get_user_form_data' ) );
	}

	/**
	 * Display settings page.
	 */
	public function settings_page() {
		// Handle user type setting
		if ( isset( $_POST['set_business_user'] ) && wp_verify_nonce( $_POST['houzez_ads_user_type_nonce'], 'houzez_ads_user_type' ) ) {
			$user_email = sanitize_email( $_POST['business_user_email'] );
			if ( $user_email ) {
				$user = get_user_by( 'email', $user_email );
				if ( $user ) {
					update_user_meta( $user->ID, 'houzez_ads_user_type', 'business' );
					echo '<div class="notice notice-success"><p>' . sprintf( __( 'User %s has been set as a business user.', 'houzez-ads-extension' ), $user_email ) . '</p></div>';
				} else {
					echo '<div class="notice notice-error"><p>' . __( 'User not found.', 'houzez-ads-extension' ) . '</p></div>';
				}
			}
		}

		// Handle business user removal
		if ( isset( $_GET['remove_business_user'] ) && wp_verify_nonce( $_GET['_wpnonce'], 'remove_business_user' ) ) {
			$user_id = absint( $_GET['remove_business_user'] );
			delete_user_meta( $user_id, 'houzez_ads_user_type' );
			echo '<div class="notice notice-success"><p>' . __( 'Business user removed successfully.', 'houzez-ads-extension' ) . '</p></div>';
			echo '<script>window.location.href = "' . admin_url( 'admin.php?page=houzez-ads-settings' ) . '";</script>';
		}

		$allowed_roles = get_option( 'houzez_ads_allowed_roles', array(
			'administrator', 'editor', 'author', 'houzez_agent', 'houzez_agency'
		) );

		$all_roles = wp_roles()->roles;
		?>
		<div class="wrap">
			<h1><?php _e( 'Ad Campaign Settings', 'houzez-ads-extension' ); ?></h1>

			<form method="post" action="options.php">
				<?php settings_fields( 'houzez_ads_settings' ); ?>

				<table class="form-table">
					<tr>
						<th scope="row"><?php _e( 'Who can create campaigns?', 'houzez-ads-extension' ); ?></th>
						<td>
							<fieldset>
								<legend class="screen-reader-text"><?php _e( 'User roles that can create campaigns', 'houzez-ads-extension' ); ?></legend>
								<?php foreach ( $all_roles as $role_key => $role_data ) : ?>
									<label>
										<input type="checkbox"
											   name="houzez_ads_allowed_roles[]"
											   value="<?php echo esc_attr( $role_key ); ?>"
											   <?php checked( in_array( $role_key, $allowed_roles ) ); ?> />
										<?php echo esc_html( $role_data['name'] ); ?>
									</label><br>
								<?php endforeach; ?>
								<p class="description">
									<?php _e( 'Select which user roles can create ad campaigns from the frontend.', 'houzez-ads-extension' ); ?>
								</p>
							</fieldset>
						</td>
					</tr>

					<tr>
						<th scope="row"><?php _e( 'Auto-approve campaigns', 'houzez-ads-extension' ); ?></th>
						<td>
							<label>
								<input type="checkbox"
									   name="houzez_ads_auto_approval"
									   value="1"
									   <?php checked( get_option( 'houzez_ads_auto_approval', false ) ); ?> />
								<?php _e( 'Automatically approve new campaigns (skip manual review)', 'houzez-ads-extension' ); ?>
							</label>
						</td>
					</tr>
				</table>

				<?php submit_button(); ?>
			</form>

			<!-- User Type Management -->
			<h2><?php _e( 'User Type Management', 'houzez-ads-extension' ); ?></h2>
			<p><?php _e( 'Set user types for the ads system. Agency and Agent roles are automatically detected.', 'houzez-ads-extension' ); ?></p>

			<form method="post" action="">
				<?php wp_nonce_field( 'houzez_ads_user_type', 'houzez_ads_user_type_nonce' ); ?>
				<table class="form-table">
					<tr>
						<th scope="row"><?php _e( 'Set User as Business/Contractor', 'houzez-ads-extension' ); ?></th>
						<td>
							<input type="text" name="business_user_email" placeholder="<?php _e( 'Enter user email', 'houzez-ads-extension' ); ?>" class="regular-text" />
							<input type="submit" name="set_business_user" class="button" value="<?php _e( 'Set as Business User', 'houzez-ads-extension' ); ?>" />
							<p class="description"><?php _e( 'Business users can create partnership banner ads with image uploads.', 'houzez-ads-extension' ); ?></p>
						</td>
					</tr>
				</table>
			</form>

			<h3><?php _e( 'Current Business Users', 'houzez-ads-extension' ); ?></h3>
			<?php
			$business_users = get_users( array(
				'meta_key' => 'houzez_ads_user_type',
				'meta_value' => 'business'
			) );

			if ( $business_users ) {
				echo '<ul>';
				foreach ( $business_users as $user ) {
					echo '<li>' . esc_html( $user->display_name ) . ' (' . esc_html( $user->user_email ) . ')
						<a href="' . admin_url( 'admin.php?page=houzez-ads-settings&remove_business_user=' . $user->ID . '&_wpnonce=' . wp_create_nonce( 'remove_business_user' ) ) . '"
						   onclick="return confirm(\'' . __( 'Are you sure?', 'houzez-ads-extension' ) . '\')">' . __( 'Remove', 'houzez-ads-extension' ) . '</a></li>';
				}
				echo '</ul>';
			} else {
				echo '<p>' . __( 'No business users found.', 'houzez-ads-extension' ) . '</p>';
			}
			?>

			<h3><?php _e( 'User Type Summary', 'houzez-ads-extension' ); ?></h3>
			<table class="widefat">
				<thead>
					<tr>
						<th><?php _e( 'User Type', 'houzez-ads-extension' ); ?></th>
						<th><?php _e( 'Available Ad Types', 'houzez-ads-extension' ); ?></th>
						<th><?php _e( 'Available Zones', 'houzez-ads-extension' ); ?></th>
						<th><?php _e( 'Duration Selection', 'houzez-ads-extension' ); ?></th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td><strong><?php _e( 'Agency/Agent', 'houzez-ads-extension' ); ?></strong></td>
						<td><?php _e( 'Profile Promotion, Property Promotion', 'houzez-ads-extension' ); ?></td>
						<td><?php _e( 'Homepage, Sidebar (Profile) / All zones (Property)', 'houzez-ads-extension' ); ?></td>
						<td><?php _e( '7, 14, 30 days', 'houzez-ads-extension' ); ?></td>
					</tr>
					<tr>
						<td><strong><?php _e( 'Regular User', 'houzez-ads-extension' ); ?></strong></td>
						<td><?php _e( 'Property Promotion only', 'houzez-ads-extension' ); ?></td>
						<td><?php _e( 'Search Results only', 'houzez-ads-extension' ); ?></td>
						<td><?php _e( 'Fixed 30 days', 'houzez-ads-extension' ); ?></td>
					</tr>
					<tr>
						<td><strong><?php _e( 'Business/Contractor', 'houzez-ads-extension' ); ?></strong></td>
						<td><?php _e( 'Business Partnership only', 'houzez-ads-extension' ); ?></td>
						<td><?php _e( 'All zones', 'houzez-ads-extension' ); ?></td>
						<td><?php _e( 'Fixed 30 days', 'houzez-ads-extension' ); ?></td>
					</tr>
				</tbody>
			</table>
		</div>
		<?php
	}

	/**
	 * Display credits management page.
	 */
	public function credits_page() {
		// Handle form submissions
		if ( isset( $_POST['action'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'houzez_ads_credits_nonce' ) ) {
			if ( $_POST['action'] === 'add_credits' && isset( $_POST['user_id'] ) && isset( $_POST['credits'] ) ) {
				$user_id = absint( $_POST['user_id'] );
				$credits = absint( $_POST['credits'] );
				$reason = sanitize_text_field( isset( $_POST['reason'] ) ? $_POST['reason'] : 'Admin credit addition' );

				if ( $user_id && $credits > 0 ) {
					if ( houzez_ads_add_user_credits( $user_id, $credits, $reason ) ) {
						echo '<div class="notice notice-success"><p>' . __( 'Credits added successfully!', 'houzez-ads-extension' ) . '</p></div>';
					} else {
						echo '<div class="notice notice-error"><p>' . __( 'Failed to add credits.', 'houzez-ads-extension' ) . '</p></div>';
					}
				}
			} elseif ( $_POST['action'] === 'assign_dummy_credits' ) {
				$credits = absint( isset( $_POST['dummy_credits'] ) ? $_POST['dummy_credits'] : 1000 );
				$updated_count = houzez_ads_assign_dummy_credits( $credits );
				echo '<div class="notice notice-success"><p>' . sprintf( __( 'Assigned %d credits to %d users.', 'houzez-ads-extension' ), $credits, $updated_count ) . '</p></div>';
			}
		}

		// Get users for dropdown
		$users = get_users( array( 'fields' => array( 'ID', 'display_name', 'user_email' ) ) );
		?>
		<div class="wrap">
			<h1><?php _e( 'Credit Management', 'houzez-ads-extension' ); ?></h1>

			<div class="card">
				<h2><?php _e( 'Add Credits to User', 'houzez-ads-extension' ); ?></h2>
				<form method="post" action="">
					<?php wp_nonce_field( 'houzez_ads_credits_nonce' ); ?>
					<input type="hidden" name="action" value="add_credits">

					<table class="form-table">
						<tr>
							<th scope="row"><?php _e( 'Select User', 'houzez-ads-extension' ); ?></th>
							<td>
								<select name="user_id" required>
									<option value=""><?php _e( 'Choose a user...', 'houzez-ads-extension' ); ?></option>
									<?php foreach ( $users as $user ) : ?>
										<option value="<?php echo $user->ID; ?>">
											<?php echo esc_html( $user->display_name . ' (' . $user->user_email . ')' ); ?>
											- <?php echo houzez_ads_format_credits( houzez_ads_get_user_credits( $user->ID ) ); ?>
										</option>
									<?php endforeach; ?>
								</select>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php _e( 'Credits to Add', 'houzez-ads-extension' ); ?></th>
							<td>
								<input type="number" name="credits" min="1" required>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php _e( 'Reason', 'houzez-ads-extension' ); ?></th>
							<td>
								<input type="text" name="reason" placeholder="<?php _e( 'Admin credit addition', 'houzez-ads-extension' ); ?>">
							</td>
						</tr>
					</table>

					<?php submit_button( __( 'Add Credits', 'houzez-ads-extension' ) ); ?>
				</form>
			</div>

			<div class="card">
				<h2><?php _e( 'Assign Dummy Credits for Testing', 'houzez-ads-extension' ); ?></h2>
				<form method="post" action="">
					<?php wp_nonce_field( 'houzez_ads_credits_nonce' ); ?>
					<input type="hidden" name="action" value="assign_dummy_credits">

					<table class="form-table">
						<tr>
							<th scope="row"><?php _e( 'Credits per User', 'houzez-ads-extension' ); ?></th>
							<td>
								<input type="number" name="dummy_credits" value="1000" min="1" required>
								<p class="description"><?php _e( 'This will add the specified credits to ALL users on the site.', 'houzez-ads-extension' ); ?></p>
							</td>
						</tr>
					</table>

					<?php submit_button( __( 'Assign to All Users', 'houzez-ads-extension' ), 'secondary' ); ?>
				</form>
			</div>

			<div class="card">
				<h2><?php _e( 'User Credit Balances', 'houzez-ads-extension' ); ?></h2>
				<table class="wp-list-table widefat fixed striped">
					<thead>
						<tr>
							<th><?php _e( 'User', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Email', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Credits', 'houzez-ads-extension' ); ?></th>
						</tr>
					</thead>
					<tbody>
						<?php foreach ( $users as $user ) : ?>
							<tr>
								<td><?php echo esc_html( $user->display_name ); ?></td>
								<td><?php echo esc_html( $user->user_email ); ?></td>
								<td><?php echo houzez_ads_format_credits( houzez_ads_get_user_credits( $user->ID ) ); ?></td>
							</tr>
						<?php endforeach; ?>
					</tbody>
				</table>
			</div>
		</div>
		<?php
	}

	/**
	 * Handle AJAX request to get user information.
	 */
	public function handle_get_user_info() {
		check_ajax_referer( 'houzez_ads_admin_nonce', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => __( 'Permission denied.', 'houzez-ads-extension' ) ) );
		}

		$user_id = absint( $_POST['user_id'] ?? 0 );
		if ( ! $user_id ) {
			wp_send_json_error( array( 'message' => __( 'Invalid user ID.', 'houzez-ads-extension' ) ) );
		}

		$user = get_userdata( $user_id );
		if ( ! $user ) {
			wp_send_json_error( array( 'message' => __( 'User not found.', 'houzez-ads-extension' ) ) );
		}

		// Get user information
		$user_type = houzez_ads_get_user_type( $user_id );
		$credit_balance = houzez_ads_get_user_credits( $user_id );
		$user_properties = houzez_ads_get_user_properties( $user_id );

		// Get active campaigns count
		$active_campaigns = get_posts( array(
			'post_type' => 'banner_campaign',
			'author' => $user_id,
			'post_status' => 'publish',
			'meta_query' => array(
				array(
					'key' => '_houzez_campaign_status',
					'value' => array( 'approved', 'pending' ),
					'compare' => 'IN'
				)
			),
			'fields' => 'ids'
		) );

		wp_send_json_success( array(
			'user_type' => $user_type,
			'credit_balance' => houzez_ads_format_credits( $credit_balance ),
			'active_campaigns' => count( $active_campaigns ),
			'total_properties' => count( $user_properties ),
			'ad_types' => houzez_ads_get_available_ad_types( $user_id ),
			'properties' => array_map( function( $property ) {
				return array(
					'ID' => $property->ID,
					'title' => $property->post_title
				);
			}, $user_properties )
		) );
	}

	/**
	 * Handle AJAX request to search users.
	 */
	public function handle_search_users() {
		check_ajax_referer( 'houzez_ads_admin_nonce', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => __( 'Permission denied.', 'houzez-ads-extension' ) ) );
		}

		$search_term = sanitize_text_field( $_POST['search'] ?? '' );

		$args = array(
			'fields' => array( 'ID', 'display_name', 'user_email' ),
			'orderby' => 'display_name',
			'order' => 'ASC',
			'number' => 20
		);

		if ( $search_term ) {
			$args['search'] = '*' . $search_term . '*';
			$args['search_columns'] = array( 'user_login', 'user_email', 'display_name' );
		}

		$users = get_users( $args );

		$results = array_map( function( $user ) {
			return array(
				'id' => $user->ID,
				'text' => $user->display_name . ' (' . $user->user_email . ')',
				'user_type' => houzez_ads_get_user_type( $user->ID )
			);
		}, $users );

		wp_send_json_success( $results );
	}

	/**
	 * Handle AJAX request to get user form data based on user type.
	 */
	public function handle_get_user_form_data() {
		check_ajax_referer( 'houzez_ads_admin_nonce', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => __( 'Permission denied.', 'houzez-ads-extension' ) ) );
		}

		$user_id = absint( $_POST['user_id'] ?? 0 );
		$user_type_override = sanitize_text_field( $_POST['user_type_override'] ?? '' );

		if ( ! $user_id ) {
			wp_send_json_error( array( 'message' => __( 'Invalid user ID.', 'houzez-ads-extension' ) ) );
		}

		// Use override if provided, otherwise detect user type
		$user_type = $user_type_override ?: houzez_ads_get_user_type( $user_id );
		$ad_types = houzez_ads_get_available_ad_types( $user_id );
		$user_properties = houzez_ads_get_user_properties( $user_id );

		// Override ad types if user type is overridden
		if ( $user_type_override ) {
			switch ( $user_type_override ) {
				case 'agency':
					$ad_types = array(
						'profile' => __( 'Agency/Profile Promotion', 'houzez-ads-extension' ),
						'property' => __( 'Property Promotion', 'houzez-ads-extension' )
					);
					break;
				case 'business':
					$ad_types = array(
						'partner' => __( 'Business Partnership', 'houzez-ads-extension' )
					);
					break;
				case 'regular':
				default:
					$ad_types = array(
						'property' => __( 'Property Promotion', 'houzez-ads-extension' )
					);
					break;
			}
		}

		wp_send_json_success( array(
			'user_type' => $user_type,
			'ad_types' => $ad_types,
			'properties' => array_map( function( $property ) {
				return array(
					'ID' => $property->ID,
					'title' => $property->post_title
				);
			}, $user_properties )
		) );
	}
}
