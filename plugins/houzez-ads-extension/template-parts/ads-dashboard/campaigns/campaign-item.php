<?php
/**
 * Campaign Item Template
 * Replicates the Houzez property item structure for campaigns
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

global $campaign_post;

if ( ! $campaign_post ) {
    return;
}

$campaign = new Houzez_Banner_Campaign( $campaign_post );
$analytics = $campaign->get_analytics();
$ad_types = houzez_ads_get_available_ad_types();
$ad_zones = houzez_ads_get_available_zones();
$statuses = houzez_ads_get_campaign_statuses();

// Calculate days remaining
$days_remaining = 0;
if ( $campaign->end_date ) {
    $days_remaining = ceil( ( strtotime( $campaign->end_date ) - current_time( 'timestamp' ) ) / ( 24 * 60 * 60 ) );
}

// Status badge styling
$status_class = 'bg-secondary';
switch ( $campaign->campaign_status ) {
    case 'approved':
        $status_class = 'bg-success';
        break;
    case 'pending':
        $status_class = 'bg-warning';
        break;
    case 'rejected':
        $status_class = 'bg-danger';
        break;
    case 'expired':
        $status_class = 'bg-dark';
        break;
}

// Payment status (if applicable)
$payment_status = get_post_meta( $campaign->id, 'payment_status', true );
$payment_status_label = '';
if ( $payment_status === 'paid' ) {
    $payment_status_label = '<span class="dashboard-label dashboard-label-small bg-success">' . __( 'PAID', 'houzez-ads-extension' ) . '</span>';
} elseif ( $payment_status === 'not_paid' ) {
    $payment_status_label = '<span class="dashboard-label dashboard-label-small bg-danger">' . __( 'NOT PAID', 'houzez-ads-extension' ) . '</span>';
}

// Featured campaign indicator
$is_featured = get_post_meta( $campaign->id, 'featured_campaign', true );
?>

<tr data-campaign-id="<?php echo esc_attr( $campaign->id ); ?>" 
    data-status="<?php echo esc_attr( $campaign->campaign_status ); ?>"
    data-zone="<?php echo esc_attr( $campaign->ad_zone ); ?>"
    data-type="<?php echo esc_attr( $campaign->ad_type ); ?>"
    data-date="<?php echo esc_attr( $campaign_post->post_date ); ?>">
    
    <!-- Select Checkbox -->
    <td data-label="<?php _e( 'Select', 'houzez-ads-extension' ); ?>">
        <label class="control control--checkbox">
            <input type="checkbox" class="control control--checkbox checkbox-delete campaign-bulk-delete" 
                   name="campaign-bulk-delete[]" value="<?php echo intval( $campaign->id ); ?>">
            <span class="control__indicator"></span>
        </label>
    </td>

    <!-- Thumbnail -->
    <td data-label="<?php _e( 'Thumbnail', 'houzez-ads-extension' ); ?>" class="px-0">
        <div class="image-holder">
            <?php echo $payment_status_label; ?>

            <?php if ( $is_featured ) : ?>
                <span class="dashboard-label dashboard-label-featured">
                    <img src="<?php echo HOUZEZ_IMAGE; ?>full-star.svg" alt="star">
                </span>
            <?php endif; ?>

            <div class="campaign-thumbnail">
                <?php if ( $campaign->banner_image ) : ?>
                    <img src="<?php echo esc_url( $campaign->banner_image ); ?>" 
                         alt="<?php echo esc_attr( $campaign->banner_alt ); ?>"
                         style="width: 80px; height: 60px; object-fit: cover; border-radius: 4px;" />
                <?php else : ?>
                    <div class="no-image-placeholder" style="width: 80px; height: 60px; background: #f8f9fa; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                        <i class="houzez-icon icon-image" style="color: #ccc; font-size: 24px;"></i>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </td>

    <!-- Campaign Details -->
    <td data-label="<?php _e( 'Campaign', 'houzez-ads-extension' ); ?>">
        <div class="text-box">
            <a class="fw-bold campaign-title" href="<?php echo esc_url( $campaign->banner_link ); ?>" target="_blank">
                <?php echo esc_html( $campaign->title ); ?>
            </a>
            <br>
            <div class="campaign-meta">
                <small class="text-muted">
                    <?php printf( __( 'Created: %s', 'houzez-ads-extension' ), date_i18n( get_option( 'date_format' ), strtotime( $campaign_post->post_date ) ) ); ?>
                </small>
                <?php if ( $campaign->end_date ) : ?>
                    <br>
                    <small class="text-muted">
                        <?php if ( $days_remaining > 0 ) : ?>
                            <span class="text-info">
                                <?php printf( _n( '%d day remaining', '%d days remaining', $days_remaining, 'houzez-ads-extension' ), $days_remaining ); ?>
                            </span>
                        <?php else : ?>
                            <span class="text-danger"><?php _e( 'Expired', 'houzez-ads-extension' ); ?></span>
                        <?php endif; ?>
                    </small>
                <?php endif; ?>
            </div>
        </div>
    </td>

    <!-- Campaign Type -->
    <td data-label="<?php _e( 'Type', 'houzez-ads-extension' ); ?>">
        <span class="badge bg-info">
            <?php echo esc_html( $ad_types[ $campaign->ad_type ] ?? $campaign->ad_type ); ?>
        </span>
    </td>

    <!-- Ad Zone -->
    <td data-label="<?php _e( 'Zone', 'houzez-ads-extension' ); ?>">
        <?php echo esc_html( $ad_zones[ $campaign->ad_zone ] ?? $campaign->ad_zone ); ?>
    </td>

    <!-- Duration -->
    <td data-label="<?php _e( 'Duration', 'houzez-ads-extension' ); ?>">
        <?php printf( _n( '%d day', '%d days', $campaign->duration, 'houzez-ads-extension' ), $campaign->duration ); ?>
        <?php if ( $campaign->end_date ) : ?>
            <br>
            <small class="text-muted">
                <?php printf( __( 'Ends: %s', 'houzez-ads-extension' ), date_i18n( get_option( 'date_format' ), strtotime( $campaign->end_date ) ) ); ?>
            </small>
        <?php endif; ?>
    </td>

    <!-- Status -->
    <td data-label="<?php _e( 'Status', 'houzez-ads-extension' ); ?>">
        <span class="badge <?php echo esc_attr( $status_class ); ?>">
            <?php echo esc_html( $statuses[ $campaign->campaign_status ] ?? $campaign->campaign_status ); ?>
        </span>
    </td>

    <!-- Analytics -->
    <td data-label="<?php _e( 'Performance', 'houzez-ads-extension' ); ?>">
        <?php if ( $campaign->campaign_status === 'approved' ) : ?>
            <div class="campaign-analytics">
                <div class="analytics-item">
                    <span class="analytics-number"><?php echo number_format( $analytics['impressions'] ); ?></span>
                    <span class="analytics-label"><?php _e( 'Views', 'houzez-ads-extension' ); ?></span>
                </div>
                <div class="analytics-item">
                    <span class="analytics-number"><?php echo number_format( $analytics['clicks'] ); ?></span>
                    <span class="analytics-label"><?php _e( 'Clicks', 'houzez-ads-extension' ); ?></span>
                </div>
                <div class="analytics-item">
                    <span class="analytics-number"><?php echo $analytics['ctr']; ?>%</span>
                    <span class="analytics-label"><?php _e( 'CTR', 'houzez-ads-extension' ); ?></span>
                </div>
            </div>
        <?php else : ?>
            <span class="text-muted"><?php _e( 'N/A', 'houzez-ads-extension' ); ?></span>
        <?php endif; ?>
    </td>

    <!-- Credits Spent -->
    <td data-label="<?php _e( 'Credits', 'houzez-ads-extension' ); ?>">
        <span class="credits-spent">
            <?php echo number_format( $campaign->price ); ?>
        </span>
    </td>

    <!-- Actions -->
    <td data-label="<?php _e( 'Actions', 'houzez-ads-extension' ); ?>" class="text-lg-center text-start px-0">
        <div class="dropdown" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="<?php _e( 'Actions', 'houzez-ads-extension' ); ?>">
            <a href="javascript:void(0)" class="action-btn" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="houzez-icon icon-navigation-menu-horizontal"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu3">
                <!-- View Campaign Stats -->
                <?php if ( $campaign->campaign_status === 'approved' ) : ?>
                    <li>
                        <a class="dropdown-item" href="<?php echo esc_url( add_query_arg( array( 'ads_page' => 'insights', 'campaign_id' => $campaign->id ), get_permalink() ) ); ?>">
                            <i class="houzez-icon icon-analytics-bars-circle"></i> <?php _e( 'View Stats', 'houzez-ads-extension' ); ?>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- Edit Campaign -->
                <?php if ( in_array( $campaign->campaign_status, array( 'draft', 'rejected' ) ) ) : ?>
                    <li>
                        <a class="dropdown-item" href="<?php echo esc_url( add_query_arg( array( 'ads_page' => 'create_campaign', 'edit_campaign' => $campaign->id ), get_permalink() ) ); ?>">
                            <i class="houzez-icon icon-pencil"></i> <?php _e( 'Edit', 'houzez-ads-extension' ); ?>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- View Target Link -->
                <?php if ( $campaign->banner_link ) : ?>
                    <li>
                        <a class="dropdown-item" href="<?php echo esc_url( $campaign->banner_link ); ?>" target="_blank">
                            <i class="houzez-icon icon-link"></i> <?php _e( 'View Target', 'houzez-ads-extension' ); ?>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- Duplicate Campaign -->
                <li>
                    <a class="dropdown-item duplicate-campaign" href="#" 
                       data-campaign-id="<?php echo esc_attr( $campaign->id ); ?>"
                       data-nonce="<?php echo wp_create_nonce( 'duplicate_campaign_nonce' ); ?>">
                        <i class="houzez-icon icon-real-estate-action-house-add"></i> <?php _e( 'Duplicate', 'houzez-ads-extension' ); ?>
                    </a>
                </li>

                <li><hr class="dropdown-divider"></li>

                <!-- Pause/Resume Campaign -->
                <?php if ( $campaign->campaign_status === 'approved' ) : ?>
                    <li>
                        <a class="dropdown-item pause-campaign" href="#" 
                           data-campaign-id="<?php echo esc_attr( $campaign->id ); ?>"
                           data-nonce="<?php echo wp_create_nonce( 'pause_campaign_nonce' ); ?>">
                            <i class="houzez-icon icon-alert-triangle"></i> <?php _e( 'Pause Campaign', 'houzez-ads-extension' ); ?>
                        </a>
                    </li>
                <?php elseif ( $campaign->campaign_status === 'paused' ) : ?>
                    <li>
                        <a class="dropdown-item resume-campaign" href="#" 
                           data-campaign-id="<?php echo esc_attr( $campaign->id ); ?>"
                           data-nonce="<?php echo wp_create_nonce( 'resume_campaign_nonce' ); ?>">
                            <i class="houzez-icon icon-upload-button"></i> <?php _e( 'Resume Campaign', 'houzez-ads-extension' ); ?>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- Delete Campaign -->
                <li>
                    <a class="dropdown-item text-danger delete-campaign" href="#"
                       data-campaign-id="<?php echo esc_attr( $campaign->id ); ?>"
                       data-campaign-title="<?php echo esc_attr( $campaign->title ); ?>"
                       data-nonce="<?php echo wp_create_nonce( 'delete_campaign_nonce' ); ?>">
                        <i class="houzez-icon icon-bin"></i> <?php _e( 'Delete', 'houzez-ads-extension' ); ?>
                    </a>
                </li>
            </ul>
        </div>
    </td>
</tr>
