<?php
/**
 * Ads Dashboard Campaigns Main Template
 * Replicates the structure of Houzez property management dashboard
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$userID = get_current_user_id();

// Get campaign status filter
$campaign_status = isset( $_GET['campaign_status'] ) ? sanitize_text_field( $_GET['campaign_status'] ) : 'all';

// Build query args based on filter
$query_args = array(
    'post_type' => 'banner_campaign',
    'author' => $userID,
    'posts_per_page' => 20,
    'orderby' => 'date',
    'order' => 'DESC',
    'paged' => get_query_var( 'paged' ) ? get_query_var( 'paged' ) : 1
);

if ( $campaign_status !== 'all' ) {
    $query_args['meta_query'] = array(
        array(
            'key' => 'campaign_status',
            'value' => $campaign_status,
            'compare' => '='
        )
    );
} else {
    $query_args['post_status'] = array( 'publish', 'draft', 'pending' );
}

$campaigns_query = new WP_Query( $query_args );
$campaigns = $campaigns_query->posts;

// Get campaign counts for tabs
$campaign_counts = houzez_ads_get_user_campaign_counts( $userID );

// Build URLs for different sections
$dashboard_add_campaign = add_query_arg( 'ads_page', 'create_campaign', get_permalink() );
$all_campaigns = add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_status' => 'all' ), get_permalink() );
$approved_campaigns = add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_status' => 'approved' ), get_permalink() );
$pending_campaigns = add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_status' => 'pending' ), get_permalink() );
$rejected_campaigns = add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_status' => 'rejected' ), get_permalink() );
$expired_campaigns = add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_status' => 'expired' ), get_permalink() );
?>

<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-md-12">
                <div class="dashboard-content-block-title">
                    <?php _e( 'My Ad Campaigns', 'houzez-ads-extension' ); ?>
                    <a href="<?php echo esc_url( $dashboard_add_campaign ); ?>" class="btn btn-primary btn-sm float-end">
                        <i class="houzez-icon icon-add-circle me-2"></i>
                        <?php _e( 'Create Campaign', 'houzez-ads-extension' ); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Campaign Status Tabs (similar to Houzez property tabs) -->
<?php houzez_ads_get_template_part( 'template-parts/ads-dashboard/campaigns/tabs' ); ?>

<!-- Campaign Filters -->
<?php houzez_ads_get_template_part( 'template-parts/ads-dashboard/campaigns/filters' ); ?>

<!-- Campaigns List -->
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <?php if ( ! empty( $campaigns ) ) : ?>
            
            <!-- Bulk Actions -->
            <div class="bulk-actions-wrap d-none">
                <div class="d-flex align-items-center justify-content-between p-3 bg-light border-bottom">
                    <div class="bulk-actions-left">
                        <span class="selected-count">0</span> <?php _e( 'campaigns selected', 'houzez-ads-extension' ); ?>
                    </div>
                    <div class="bulk-actions-right">
                        <select class="form-select form-select-sm me-2" id="bulk-action-select">
                            <option value=""><?php _e( 'Bulk Actions', 'houzez-ads-extension' ); ?></option>
                            <option value="delete"><?php _e( 'Delete', 'houzez-ads-extension' ); ?></option>
                            <option value="pause"><?php _e( 'Pause', 'houzez-ads-extension' ); ?></option>
                            <option value="resume"><?php _e( 'Resume', 'houzez-ads-extension' ); ?></option>
                        </select>
                        <button type="button" class="btn btn-sm btn-primary" id="apply-bulk-action">
                            <?php _e( 'Apply', 'houzez-ads-extension' ); ?>
                        </button>
                        <button type="button" class="btn btn-sm btn-secondary" id="cancel-bulk-selection">
                            <?php _e( 'Cancel', 'houzez-ads-extension' ); ?>
                        </button>
                    </div>
                </div>
            </div>

            <div class="dashboard-table-wrap">
                <table class="table table-hover campaign-table">
                    <thead>
                        <tr>
                            <th width="40">
                                <label class="control control--checkbox">
                                    <input type="checkbox" id="select-all-campaigns" class="control control--checkbox">
                                    <span class="control__indicator"></span>
                                </label>
                            </th>
                            <th width="100"><?php _e( 'Thumbnail', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Campaign', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Type', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Zone', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Duration', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Status', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Performance', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Credits', 'houzez-ads-extension' ); ?></th>
                            <th width="80"><?php _e( 'Actions', 'houzez-ads-extension' ); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ( $campaigns as $campaign_post ) : 
                            // Set global variable for campaign item template
                            $GLOBALS['campaign_post'] = $campaign_post;
                            houzez_ads_get_template_part( 'template-parts/ads-dashboard/campaigns/campaign-item' );
                        endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ( $campaigns_query->max_num_pages > 1 ) : ?>
                <div class="dashboard-pagination-wrap">
                    <?php
                    $pagination_args = array(
                        'total' => $campaigns_query->max_num_pages,
                        'current' => max( 1, get_query_var( 'paged' ) ),
                        'format' => '?paged=%#%',
                        'show_all' => false,
                        'end_size' => 1,
                        'mid_size' => 2,
                        'prev_next' => true,
                        'prev_text' => __( '&laquo; Previous', 'houzez-ads-extension' ),
                        'next_text' => __( 'Next &raquo;', 'houzez-ads-extension' ),
                        'type' => 'array'
                    );
                    
                    $pagination_links = paginate_links( $pagination_args );
                    
                    if ( $pagination_links ) :
                    ?>
                        <nav aria-label="<?php _e( 'Campaigns pagination', 'houzez-ads-extension' ); ?>">
                            <ul class="pagination justify-content-center">
                                <?php foreach ( $pagination_links as $link ) : ?>
                                    <li class="page-item"><?php echo $link; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

        <?php else : ?>
            <!-- Empty State -->
            <div class="dashboard-empty-state text-center py-5">
                <div class="empty-state-icon mb-4">
                    <i class="houzez-icon icon-megaphone" style="font-size: 64px; color: #ddd;"></i>
                </div>
                <h4><?php _e( 'No campaigns found', 'houzez-ads-extension' ); ?></h4>
                <p class="text-muted mb-4">
                    <?php 
                    if ( $campaign_status === 'all' ) {
                        _e( 'You haven\'t created any campaigns yet. Start promoting your properties with targeted ads.', 'houzez-ads-extension' );
                    } else {
                        printf( __( 'No campaigns found with status: %s', 'houzez-ads-extension' ), ucfirst( $campaign_status ) );
                    }
                    ?>
                </p>
                <a href="<?php echo esc_url( $dashboard_add_campaign ); ?>" class="btn btn-primary">
                    <i class="houzez-icon icon-add-circle me-2"></i>
                    <?php _e( 'Create Your First Campaign', 'houzez-ads-extension' ); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Campaign management functionality
    houzezAdsCampaigns.init();
    
    // Search functionality
    $('#campaign-search').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('.campaign-table tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Filter functionality
    $('.campaign-filter').on('change', function() {
        houzezAdsCampaigns.applyFilters();
    });

    // Bulk selection
    $('#select-all-campaigns').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('.campaign-bulk-delete:visible').prop('checked', isChecked);
        houzezAdsCampaigns.updateBulkActions();
    });

    $(document).on('change', '.campaign-bulk-delete', function() {
        houzezAdsCampaigns.updateBulkActions();
    });

    // Bulk actions
    $('#apply-bulk-action').on('click', function() {
        houzezAdsCampaigns.applyBulkAction();
    });

    $('#cancel-bulk-selection').on('click', function() {
        $('.campaign-bulk-delete').prop('checked', false);
        $('#select-all-campaigns').prop('checked', false);
        houzezAdsCampaigns.updateBulkActions();
    });
});
</script>
