<?php
/**
 * Create Campaign Template
 * Replicates the Houzez property creation workflow with credit check
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$userID = get_current_user_id();
$user_credits = houzez_ads_get_user_credits( $userID );

// Check if editing existing campaign
$edit_campaign_id = isset( $_GET['edit_campaign'] ) ? absint( $_GET['edit_campaign'] ) : 0;
$campaign = null;

if ( $edit_campaign_id ) {
    $campaign_post = get_post( $edit_campaign_id );
    if ( $campaign_post && $campaign_post->post_author == $userID ) {
        $campaign = new Houzez_Banner_Campaign( $campaign_post );
    } else {
        // Redirect if user doesn't own this campaign
        wp_redirect( houzez_ads_get_dashboard_url( 'campaigns' ) );
        exit;
    }
}

// Credit check for new campaigns (similar to Houzez package check)
$min_credits_required = 10; // Minimum credits needed to create a campaign
$show_credit_warning = ! $edit_campaign_id && $user_credits < $min_credits_required;

$dashboard_campaigns = houzez_ads_get_dashboard_url( 'campaigns' );
$dashboard_credits = houzez_ads_get_dashboard_url( 'credits' );
?>

<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-md-12">
                <div class="dashboard-content-block-title">
                    <?php if ( $edit_campaign_id ) : ?>
                        <?php _e( 'Edit Campaign', 'houzez-ads-extension' ); ?>
                        <a href="<?php echo esc_url( $dashboard_campaigns ); ?>" class="btn btn-secondary btn-sm float-end">
                            <i class="houzez-icon icon-arrow-left-1 me-2"></i>
                            <?php _e( 'Back to Campaigns', 'houzez-ads-extension' ); ?>
                        </a>
                    <?php else : ?>
                        <?php _e( 'Create New Campaign', 'houzez-ads-extension' ); ?>
                        <a href="<?php echo esc_url( $dashboard_campaigns ); ?>" class="btn btn-secondary btn-sm float-end">
                            <i class="houzez-icon icon-arrow-left-1 me-2"></i>
                            <?php _e( 'Back to Campaigns', 'houzez-ads-extension' ); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Credit Check Warning (similar to Houzez package warning) -->
<?php if ( $show_credit_warning ) : ?>
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="alert alert-warning d-flex align-items-center" role="alert">
            <div class="alert-icon me-3">
                <i class="houzez-icon icon-alert-triangle" style="font-size: 24px;"></i>
            </div>
            <div class="alert-content flex-grow-1">
                <h5 class="alert-heading"><?php _e( 'Insufficient Credits', 'houzez-ads-extension' ); ?></h5>
                <p class="mb-2">
                    <?php printf( 
                        __( 'You need at least %d credits to create a campaign. You currently have %d credits.', 'houzez-ads-extension' ), 
                        $min_credits_required, 
                        $user_credits 
                    ); ?>
                </p>
                <p class="mb-0">
                    <?php _e( 'Please purchase credits to continue with campaign creation.', 'houzez-ads-extension' ); ?>
                </p>
            </div>
            <div class="alert-actions">
                <a href="<?php echo esc_url( $dashboard_credits ); ?>" class="btn btn-primary">
                    <i class="houzez-icon icon-add-circle me-2"></i>
                    <?php _e( 'Buy Credits', 'houzez-ads-extension' ); ?>
                </a>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Campaign Creation Form -->
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <form id="houzez-ads-campaign-form" method="post" enctype="multipart/form-data" 
              <?php echo $show_credit_warning ? 'style="opacity: 0.5; pointer-events: none;"' : ''; ?>>
            
            <?php wp_nonce_field( 'houzez_ads_create_campaign', 'houzez_ads_campaign_nonce' ); ?>
            
            <?php if ( $edit_campaign_id ) : ?>
                <input type="hidden" name="campaign_id" value="<?php echo esc_attr( $edit_campaign_id ); ?>">
                <input type="hidden" name="action" value="edit_campaign">
            <?php else : ?>
                <input type="hidden" name="action" value="create_campaign">
            <?php endif; ?>

            <!-- Campaign Basic Information -->
            <div class="row">
                <div class="col-md-12">
                    <div class="dashboard-content-block-title">
                        <?php _e( 'Campaign Information', 'houzez-ads-extension' ); ?>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6 col-md-12">
                    <div class="form-group">
                        <label for="campaign_title"><?php _e( 'Campaign Title', 'houzez-ads-extension' ); ?> <span class="required">*</span></label>
                        <input type="text" id="campaign_title" name="campaign_title" class="form-control" 
                               value="<?php echo $campaign ? esc_attr( $campaign->title ) : ''; ?>" 
                               placeholder="<?php _e( 'Enter campaign title', 'houzez-ads-extension' ); ?>" required>
                    </div>
                </div>

                <div class="col-lg-6 col-md-12">
                    <div class="form-group">
                        <label for="ad_type"><?php _e( 'Campaign Type', 'houzez-ads-extension' ); ?> <span class="required">*</span></label>
                        <select id="ad_type" name="ad_type" class="selectpicker form-control" required>
                            <option value=""><?php _e( 'Select Campaign Type', 'houzez-ads-extension' ); ?></option>
                            <?php 
                            $ad_types = houzez_ads_get_available_ad_types();
                            $current_type = $campaign ? $campaign->ad_type : '';
                            foreach ( $ad_types as $key => $label ) : ?>
                                <option value="<?php echo esc_attr( $key ); ?>" <?php selected( $current_type, $key ); ?>>
                                    <?php echo esc_html( $label ); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6 col-md-12">
                    <div class="form-group">
                        <label for="ad_zone"><?php _e( 'Ad Zone', 'houzez-ads-extension' ); ?> <span class="required">*</span></label>
                        <select id="ad_zone" name="ad_zone" class="selectpicker form-control" required>
                            <option value=""><?php _e( 'Select Ad Zone', 'houzez-ads-extension' ); ?></option>
                            <?php 
                            $ad_zones = houzez_ads_get_available_zones();
                            $current_zone = $campaign ? $campaign->ad_zone : '';
                            foreach ( $ad_zones as $key => $label ) : ?>
                                <option value="<?php echo esc_attr( $key ); ?>" <?php selected( $current_zone, $key ); ?>>
                                    <?php echo esc_html( $label ); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="col-lg-6 col-md-12">
                    <div class="form-group">
                        <label for="duration"><?php _e( 'Campaign Duration', 'houzez-ads-extension' ); ?> <span class="required">*</span></label>
                        <select id="duration" name="duration" class="selectpicker form-control" required>
                            <option value=""><?php _e( 'Select Duration', 'houzez-ads-extension' ); ?></option>
                            <?php 
                            $durations = array(
                                '7' => __( '1 Week (7 days)', 'houzez-ads-extension' ),
                                '14' => __( '2 Weeks (14 days)', 'houzez-ads-extension' ),
                                '30' => __( '1 Month (30 days)', 'houzez-ads-extension' ),
                                '60' => __( '2 Months (60 days)', 'houzez-ads-extension' ),
                                '90' => __( '3 Months (90 days)', 'houzez-ads-extension' )
                            );
                            $current_duration = $campaign ? $campaign->duration : '';
                            foreach ( $durations as $days => $label ) : ?>
                                <option value="<?php echo esc_attr( $days ); ?>" <?php selected( $current_duration, $days ); ?>>
                                    <?php echo esc_html( $label ); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Campaign Content -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="dashboard-content-block-title">
                        <?php _e( 'Campaign Content', 'houzez-ads-extension' ); ?>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6 col-md-12">
                    <div class="form-group">
                        <label for="banner_image"><?php _e( 'Banner Image', 'houzez-ads-extension' ); ?> <span class="required">*</span></label>
                        <div class="file-upload-wrap">
                            <input type="file" id="banner_image" name="banner_image" class="form-control" 
                                   accept="image/*" <?php echo ! $campaign ? 'required' : ''; ?>>
                            <small class="form-text text-muted">
                                <?php _e( 'Recommended size: 728x90px for banner ads. Max file size: 2MB.', 'houzez-ads-extension' ); ?>
                            </small>
                            
                            <?php if ( $campaign && $campaign->banner_image ) : ?>
                                <div class="current-image mt-2">
                                    <img src="<?php echo esc_url( $campaign->banner_image ); ?>" 
                                         alt="<?php echo esc_attr( $campaign->banner_alt ); ?>"
                                         style="max-width: 200px; height: auto; border: 1px solid #ddd; border-radius: 4px;">
                                    <p class="small text-muted mt-1"><?php _e( 'Current image (leave empty to keep)', 'houzez-ads-extension' ); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-md-12">
                    <div class="form-group">
                        <label for="banner_link"><?php _e( 'Target URL', 'houzez-ads-extension' ); ?> <span class="required">*</span></label>
                        <input type="url" id="banner_link" name="banner_link" class="form-control" 
                               value="<?php echo $campaign ? esc_attr( $campaign->banner_link ) : ''; ?>" 
                               placeholder="<?php _e( 'https://example.com', 'houzez-ads-extension' ); ?>" required>
                        <small class="form-text text-muted">
                            <?php _e( 'Where users will be redirected when they click your ad.', 'houzez-ads-extension' ); ?>
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="banner_alt"><?php _e( 'Alt Text', 'houzez-ads-extension' ); ?></label>
                        <input type="text" id="banner_alt" name="banner_alt" class="form-control" 
                               value="<?php echo $campaign ? esc_attr( $campaign->banner_alt ) : ''; ?>" 
                               placeholder="<?php _e( 'Describe your ad image', 'houzez-ads-extension' ); ?>">
                        <small class="form-text text-muted">
                            <?php _e( 'Alternative text for accessibility and SEO.', 'houzez-ads-extension' ); ?>
                        </small>
                    </div>
                </div>
            </div>

            <!-- Campaign Pricing -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="dashboard-content-block-title">
                        <?php _e( 'Campaign Pricing', 'houzez-ads-extension' ); ?>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="pricing-calculator bg-light p-4 rounded">
                        <div class="row align-items-center">
                            <div class="col-lg-8">
                                <div class="pricing-details">
                                    <h5><?php _e( 'Campaign Cost', 'houzez-ads-extension' ); ?></h5>
                                    <p class="text-muted mb-2"><?php _e( 'The cost will be calculated based on your selected zone and duration.', 'houzez-ads-extension' ); ?></p>
                                    <div class="pricing-breakdown">
                                        <div class="pricing-item">
                                            <span class="pricing-label"><?php _e( 'Base Cost:', 'houzez-ads-extension' ); ?></span>
                                            <span class="pricing-value" id="base-cost">--</span>
                                        </div>
                                        <div class="pricing-item">
                                            <span class="pricing-label"><?php _e( 'Duration Multiplier:', 'houzez-ads-extension' ); ?></span>
                                            <span class="pricing-value" id="duration-multiplier">--</span>
                                        </div>
                                        <div class="pricing-item total">
                                            <span class="pricing-label"><?php _e( 'Total Credits:', 'houzez-ads-extension' ); ?></span>
                                            <span class="pricing-value" id="total-cost">--</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 text-lg-end">
                                <div class="user-credits-info">
                                    <p class="mb-1"><?php _e( 'Your Credits:', 'houzez-ads-extension' ); ?></p>
                                    <h4 class="text-primary mb-2"><?php echo number_format( $user_credits ); ?></h4>
                                    <a href="<?php echo esc_url( $dashboard_credits ); ?>" class="btn btn-outline-primary btn-sm">
                                        <?php _e( 'Buy More Credits', 'houzez-ads-extension' ); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="form-actions d-flex justify-content-between">
                        <div class="form-actions-left">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back();">
                                <i class="houzez-icon icon-arrow-left-1 me-2"></i>
                                <?php _e( 'Cancel', 'houzez-ads-extension' ); ?>
                            </button>
                        </div>
                        <div class="form-actions-right">
                            <button type="submit" name="save_draft" class="btn btn-outline-primary me-2">
                                <i class="houzez-icon icon-save me-2"></i>
                                <?php _e( 'Save as Draft', 'houzez-ads-extension' ); ?>
                            </button>
                            <button type="submit" name="submit_campaign" class="btn btn-primary" id="submit-campaign-btn">
                                <i class="houzez-icon icon-check-circle-1 me-2"></i>
                                <?php echo $edit_campaign_id ? __( 'Update Campaign', 'houzez-ads-extension' ) : __( 'Submit for Review', 'houzez-ads-extension' ); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </form>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Initialize campaign creation functionality
    houzezAdsCampaignCreation.init();
    
    // Real-time pricing calculation
    $('#ad_zone, #duration, #ad_type').on('change', function() {
        calculateCampaignCost();
    });
    
    function calculateCampaignCost() {
        var zone = $('#ad_zone').val();
        var duration = $('#duration').val();
        var adType = $('#ad_type').val();
        
        if (zone && duration && adType) {
            $.ajax({
                url: houzez_ads_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'houzez_ads_calculate_cost',
                    zone: zone,
                    duration: duration,
                    ad_type: adType,
                    nonce: houzez_ads_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $('#base-cost').text(response.data.base_cost + ' credits');
                        $('#duration-multiplier').text('x' + response.data.duration_multiplier);
                        $('#total-cost').text(response.data.total_cost + ' credits');
                        
                        // Check if user has enough credits
                        var userCredits = <?php echo $user_credits; ?>;
                        var totalCost = response.data.total_cost;
                        
                        if (userCredits < totalCost) {
                            $('#submit-campaign-btn').prop('disabled', true)
                                .html('<i class="houzez-icon icon-alert-triangle me-2"></i><?php _e( "Insufficient Credits", "houzez-ads-extension" ); ?>');
                        } else {
                            $('#submit-campaign-btn').prop('disabled', false)
                                .html('<i class="houzez-icon icon-check-circle-1 me-2"></i><?php echo $edit_campaign_id ? __( "Update Campaign", "houzez-ads-extension" ) : __( "Submit for Review", "houzez-ads-extension" ); ?>');
                        }
                    }
                }
            });
        }
    }
    
    // Initial calculation if editing
    <?php if ( $edit_campaign_id ) : ?>
        calculateCampaignCost();
    <?php endif; ?>
});
</script>
