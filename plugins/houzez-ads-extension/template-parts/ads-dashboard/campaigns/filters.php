<?php
/**
 * Campaign Filters Template
 * Replicates the Houzez property filters structure
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$ad_zones = houzez_ads_get_available_zones();
$ad_types = houzez_ads_get_available_ad_types();
?>

<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="houzez-table-filters">
            <div class="row">
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <div class="dashboard-search-filter">
                        <span><i class="houzez-icon icon-search-1"></i></span>
                        <input type="text" id="campaign-search" class="form-control dashboard-search" 
                               placeholder="<?php _e( 'Search campaigns...', 'houzez-ads-extension' ); ?>">
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <select id="campaign-zone-filter" class="selectpicker form-control campaign-filter" 
                            title="<?php _e( 'Filter by Zone', 'houzez-ads-extension' ); ?>">
                        <option value=""><?php _e( 'All Zones', 'houzez-ads-extension' ); ?></option>
                        <?php foreach ( $ad_zones as $key => $label ) : ?>
                            <option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <select id="campaign-type-filter" class="selectpicker form-control campaign-filter" 
                            title="<?php _e( 'Filter by Type', 'houzez-ads-extension' ); ?>">
                        <option value=""><?php _e( 'All Types', 'houzez-ads-extension' ); ?></option>
                        <?php foreach ( $ad_types as $key => $label ) : ?>
                            <option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <select id="campaign-date-filter" class="selectpicker form-control campaign-filter" 
                            title="<?php _e( 'Filter by Date', 'houzez-ads-extension' ); ?>">
                        <option value=""><?php _e( 'All Dates', 'houzez-ads-extension' ); ?></option>
                        <option value="today"><?php _e( 'Today', 'houzez-ads-extension' ); ?></option>
                        <option value="week"><?php _e( 'This Week', 'houzez-ads-extension' ); ?></option>
                        <option value="month"><?php _e( 'This Month', 'houzez-ads-extension' ); ?></option>
                        <option value="year"><?php _e( 'This Year', 'houzez-ads-extension' ); ?></option>
                    </select>
                </div>
                
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <select id="campaign-sort-filter" class="selectpicker form-control campaign-filter" 
                            title="<?php _e( 'Sort by', 'houzez-ads-extension' ); ?>">
                        <option value="date_desc"><?php _e( 'Newest First', 'houzez-ads-extension' ); ?></option>
                        <option value="date_asc"><?php _e( 'Oldest First', 'houzez-ads-extension' ); ?></option>
                        <option value="title_asc"><?php _e( 'Title A-Z', 'houzez-ads-extension' ); ?></option>
                        <option value="title_desc"><?php _e( 'Title Z-A', 'houzez-ads-extension' ); ?></option>
                        <option value="performance"><?php _e( 'Best Performance', 'houzez-ads-extension' ); ?></option>
                    </select>
                </div>
                
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <div class="filter-actions">
                        <button type="button" class="btn btn-outline-secondary" id="clear-filters">
                            <i class="houzez-icon icon-close me-2"></i>
                            <?php _e( 'Clear Filters', 'houzez-ads-extension' ); ?>
                        </button>
                        <button type="button" class="btn btn-primary" id="apply-filters">
                            <i class="houzez-icon icon-search-1 me-2"></i>
                            <?php _e( 'Apply Filters', 'houzez-ads-extension' ); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Clear filters functionality
    $('#clear-filters').on('click', function() {
        $('#campaign-search').val('');
        $('.campaign-filter').val('').trigger('change');
        
        // Reset selectpicker if available
        if ($.fn.selectpicker) {
            $('.selectpicker').selectpicker('refresh');
        }
        
        // Show all campaigns
        $('.campaign-table tbody tr').show();
        
        // Update visible count
        houzezAdsCampaigns.updateVisibleCount();
    });
    
    // Apply filters functionality
    $('#apply-filters').on('click', function() {
        houzezAdsCampaigns.applyFilters();
    });
    
    // Initialize selectpicker if available
    if ($.fn.selectpicker) {
        $('.selectpicker').selectpicker({
            style: 'btn-outline-secondary',
            size: 4
        });
    }
});
</script>
