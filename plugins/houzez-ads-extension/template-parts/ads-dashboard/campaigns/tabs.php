<?php
/**
 * Campaign Status Tabs Template
 * Replicates the <PERSON><PERSON><PERSON> property status tabs structure
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$userID = get_current_user_id();
$campaign_status = isset( $_GET['campaign_status'] ) ? sanitize_text_field( $_GET['campaign_status'] ) : 'all';
$campaign_counts = houzez_ads_get_user_campaign_counts( $userID );

// Build URLs for different status tabs
$all_campaigns = add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_status' => 'all' ), get_permalink() );
$approved_campaigns = add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_status' => 'approved' ), get_permalink() );
$pending_campaigns = add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_status' => 'pending' ), get_permalink() );
$rejected_campaigns = add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_status' => 'rejected' ), get_permalink() );
$expired_campaigns = add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_status' => 'expired' ), get_permalink() );

// Set active states
$ac_all = $campaign_status === 'all' ? 'active' : '';
$ac_approved = $campaign_status === 'approved' ? 'active' : '';
$ac_pending = $campaign_status === 'pending' ? 'active' : '';
$ac_rejected = $campaign_status === 'rejected' ? 'active' : '';
$ac_expired = $campaign_status === 'expired' ? 'active' : '';
?>

<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="dashboard-tabs-wrap">
            <ul class="nav nav-tabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link <?php echo esc_attr( $ac_all ); ?>" 
                       href="<?php echo esc_url( $all_campaigns ); ?>">
                        <?php _e( 'All', 'houzez-ads-extension' ); ?>
                        <span class="badge bg-secondary ms-1"><?php echo $campaign_counts['total']; ?></span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo esc_attr( $ac_approved ); ?>" 
                       href="<?php echo esc_url( $approved_campaigns ); ?>">
                        <?php _e( 'Approved', 'houzez-ads-extension' ); ?>
                        <span class="badge bg-success ms-1"><?php echo $campaign_counts['approved']; ?></span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo esc_attr( $ac_pending ); ?>" 
                       href="<?php echo esc_url( $pending_campaigns ); ?>">
                        <?php _e( 'Pending Review', 'houzez-ads-extension' ); ?>
                        <span class="badge bg-warning ms-1"><?php echo $campaign_counts['pending']; ?></span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo esc_attr( $ac_rejected ); ?>" 
                       href="<?php echo esc_url( $rejected_campaigns ); ?>">
                        <?php _e( 'Rejected', 'houzez-ads-extension' ); ?>
                        <span class="badge bg-danger ms-1"><?php echo $campaign_counts['rejected']; ?></span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo esc_attr( $ac_expired ); ?>" 
                       href="<?php echo esc_url( $expired_campaigns ); ?>">
                        <?php _e( 'Expired', 'houzez-ads-extension' ); ?>
                        <span class="badge bg-dark ms-1"><?php echo $campaign_counts['expired']; ?></span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
