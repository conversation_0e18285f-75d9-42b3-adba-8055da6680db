<?php
/**
 * Credits and Billing Dashboard Template
 * Replicates the Houzez membership/package management interface
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$current_user_id = get_current_user_id();
$user_credits = houzez_ads_get_user_credits( $current_user_id );

// Get credit transaction history
$credit_transactions = houzez_ads_get_user_credit_transactions( $current_user_id, 20 );

// Get credit packages
$credit_packages = houzez_ads_get_credit_packages();

// Get recent campaign spending
$recent_spending = houzez_ads_get_user_recent_spending( $current_user_id, 30 );

$dashboard_campaigns = houzez_ads_get_dashboard_url( 'campaigns' );
?>

<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-md-12">
                <div class="dashboard-content-block-title">
                    <?php _e( 'Credits & Billing', 'houzez-ads-extension' ); ?>
                    <a href="<?php echo esc_url( $dashboard_campaigns ); ?>" class="btn btn-secondary btn-sm float-end">
                        <i class="houzez-icon icon-arrow-left-1 me-2"></i>
                        <?php _e( 'Back to Campaigns', 'houzez-ads-extension' ); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Credit Balance Overview (similar to Houzez package overview) -->
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="dashboard-statistic-block">
                    <div class="statistic-block-inner">
                        <div class="statistic-block-number text-primary">
                            <?php echo number_format( $user_credits ); ?>
                        </div>
                        <div class="statistic-block-title">
                            <?php _e( 'Available Credits', 'houzez-ads-extension' ); ?>
                        </div>
                    </div>
                    <div class="statistic-block-icon">
                        <i class="houzez-icon icon-accounting-document"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="dashboard-statistic-block">
                    <div class="statistic-block-inner">
                        <div class="statistic-block-number text-success">
                            <?php echo number_format( $recent_spending['total_purchased'] ); ?>
                        </div>
                        <div class="statistic-block-title">
                            <?php _e( 'Credits Purchased', 'houzez-ads-extension' ); ?>
                            <small class="d-block text-muted"><?php _e( 'Last 30 days', 'houzez-ads-extension' ); ?></small>
                        </div>
                    </div>
                    <div class="statistic-block-icon">
                        <i class="houzez-icon icon-add-circle"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="dashboard-statistic-block">
                    <div class="statistic-block-inner">
                        <div class="statistic-block-number text-warning">
                            <?php echo number_format( $recent_spending['total_spent'] ); ?>
                        </div>
                        <div class="statistic-block-title">
                            <?php _e( 'Credits Spent', 'houzez-ads-extension' ); ?>
                            <small class="d-block text-muted"><?php _e( 'Last 30 days', 'houzez-ads-extension' ); ?></small>
                        </div>
                    </div>
                    <div class="statistic-block-icon">
                        <i class="houzez-icon icon-megaphone"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="dashboard-statistic-block">
                    <div class="statistic-block-inner">
                        <div class="statistic-block-number text-info">
                            <?php echo number_format( $recent_spending['active_campaigns'] ); ?>
                        </div>
                        <div class="statistic-block-title">
                            <?php _e( 'Active Campaigns', 'houzez-ads-extension' ); ?>
                            <small class="d-block text-muted"><?php _e( 'Currently running', 'houzez-ads-extension' ); ?></small>
                        </div>
                    </div>
                    <div class="statistic-block-icon">
                        <i class="houzez-icon icon-check-circle-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Credit Packages (similar to Houzez membership packages) -->
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-md-12">
                <div class="dashboard-content-block-title">
                    <?php _e( 'Credit Packages', 'houzez-ads-extension' ); ?>
                </div>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ( $credit_packages as $package ) : ?>
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <div class="membership-package-wrap credit-package-card">
                        <div class="membership-package">
                            <?php if ( $package['popular'] ) : ?>
                                <div class="package-popular-tag">
                                    <span><?php _e( 'Most Popular', 'houzez-ads-extension' ); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <div class="package-header">
                                <h3 class="package-title"><?php echo number_format( $package['credits'] ); ?> <?php _e( 'Credits', 'houzez-ads-extension' ); ?></h3>
                                <div class="package-price">
                                    <span class="price-currency">$</span>
                                    <span class="price-amount"><?php echo number_format( $package['price'], 2 ); ?></span>
                                </div>
                                
                                <?php if ( $package['bonus_credits'] > 0 ) : ?>
                                    <div class="package-bonus">
                                        <span class="bonus-badge">
                                            <?php printf( __( '+%d Bonus Credits', 'houzez-ads-extension' ), $package['bonus_credits'] ); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="package-features">
                                <ul class="package-features-list">
                                    <?php foreach ( $package['features'] as $feature ) : ?>
                                        <li>
                                            <i class="houzez-icon icon-check-circle-1"></i>
                                            <?php echo esc_html( $feature ); ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            
                            <div class="package-footer">
                                <button type="button" class="btn btn-primary btn-block purchase-credits-btn"
                                        data-package-id="<?php echo esc_attr( $package['id'] ); ?>"
                                        data-credits="<?php echo esc_attr( $package['credits'] ); ?>"
                                        data-price="<?php echo esc_attr( $package['price'] ); ?>">
                                    <i class="houzez-icon icon-add-circle me-2"></i>
                                    <?php _e( 'Purchase Package', 'houzez-ads-extension' ); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<!-- Transaction History -->
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-md-12">
                <div class="dashboard-content-block-title">
                    <?php _e( 'Transaction History', 'houzez-ads-extension' ); ?>
                </div>
            </div>
        </div>
        
        <?php if ( ! empty( $credit_transactions ) ) : ?>
            <div class="dashboard-table-wrap">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><?php _e( 'Date', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Type', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Description', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Credits', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Balance', 'houzez-ads-extension' ); ?></th>
                            <th><?php _e( 'Status', 'houzez-ads-extension' ); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ( $credit_transactions as $transaction ) : ?>
                            <tr>
                                <td>
                                    <?php echo date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $transaction->created_at ) ); ?>
                                </td>
                                <td>
                                    <span class="badge <?php echo $transaction->type === 'purchase' ? 'bg-success' : 'bg-warning'; ?>">
                                        <?php echo $transaction->type === 'purchase' ? __( 'Purchase', 'houzez-ads-extension' ) : __( 'Campaign', 'houzez-ads-extension' ); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo esc_html( $transaction->description ); ?>
                                    <?php if ( $transaction->campaign_id ) : ?>
                                        <br>
                                        <small class="text-muted">
                                            <a href="<?php echo esc_url( add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_id' => $transaction->campaign_id ), get_permalink() ) ); ?>">
                                                <?php _e( 'View Campaign', 'houzez-ads-extension' ); ?>
                                            </a>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="<?php echo $transaction->type === 'purchase' ? 'text-success' : 'text-warning'; ?>">
                                        <?php echo $transaction->type === 'purchase' ? '+' : '-'; ?><?php echo number_format( abs( $transaction->credits ) ); ?>
                                    </span>
                                </td>
                                <td><?php echo number_format( $transaction->balance_after ); ?></td>
                                <td>
                                    <span class="badge <?php echo $transaction->status === 'completed' ? 'bg-success' : 'bg-warning'; ?>">
                                        <?php echo ucfirst( $transaction->status ); ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="text-center mt-4">
                <a href="<?php echo esc_url( add_query_arg( 'view_all_transactions', '1' ) ); ?>" class="btn btn-outline-primary">
                    <?php _e( 'View All Transactions', 'houzez-ads-extension' ); ?>
                </a>
            </div>
        <?php else : ?>
            <div class="dashboard-empty-state text-center py-5">
                <div class="empty-state-icon mb-4">
                    <i class="houzez-icon icon-accounting-document" style="font-size: 64px; color: #ddd;"></i>
                </div>
                <h4><?php _e( 'No transactions yet', 'houzez-ads-extension' ); ?></h4>
                <p class="text-muted mb-4">
                    <?php _e( 'Your credit purchase and campaign spending history will appear here.', 'houzez-ads-extension' ); ?>
                </p>
                <button type="button" class="btn btn-primary purchase-credits-btn" data-package-id="professional">
                    <i class="houzez-icon icon-add-circle me-2"></i>
                    <?php _e( 'Purchase Your First Credits', 'houzez-ads-extension' ); ?>
                </button>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Handle credit package purchase
    $('.purchase-credits-btn').on('click', function(e) {
        e.preventDefault();
        
        var packageId = $(this).data('package-id');
        var credits = $(this).data('credits');
        var price = $(this).data('price');
        var $button = $(this);
        
        // Show loading state
        $button.prop('disabled', true).html('<i class="houzez-icon icon-loader-1 me-2"></i><?php _e( "Processing...", "houzez-ads-extension" ); ?>');
        
        // Process purchase
        $.ajax({
            url: houzez_ads_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'houzez_ads_purchase_credits',
                package_id: packageId,
                nonce: houzez_ads_ajax.nonce
            },
            success: function(response) {
                if (response.success && response.data.redirect_url) {
                    window.location.href = response.data.redirect_url;
                } else {
                    alert(response.data.message || '<?php _e( "Error processing request", "houzez-ads-extension" ); ?>');
                    $button.prop('disabled', false).html('<i class="houzez-icon icon-add-circle me-2"></i><?php _e( "Purchase Package", "houzez-ads-extension" ); ?>');
                }
            },
            error: function() {
                alert('<?php _e( "Error processing request", "houzez-ads-extension" ); ?>');
                $button.prop('disabled', false).html('<i class="houzez-icon icon-add-circle me-2"></i><?php _e( "Purchase Package", "houzez-ads-extension" ); ?>');
            }
        });
    });
});
</script>

<style>
.credit-package-card {
    margin-bottom: 30px;
}

.membership-package {
    position: relative;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.membership-package:hover {
    border-color: #007bff;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 123, 255, 0.1);
}

.package-popular-tag {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.package-popular-tag span {
    background: #007bff;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.package-header .package-title {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
}

.package-price {
    font-size: 36px;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 15px;
}

.package-price .price-currency {
    font-size: 18px;
    vertical-align: top;
}

.package-bonus {
    margin-bottom: 20px;
}

.bonus-badge {
    background: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.package-features-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.package-features-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.package-features-list li:last-child {
    border-bottom: none;
}

.package-features-list i {
    color: #28a745;
    margin-right: 10px;
}

.package-footer {
    margin-top: 30px;
}

.btn-block {
    width: 100%;
}
</style>
