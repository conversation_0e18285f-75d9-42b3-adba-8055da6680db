<?php
/**
 * Campaign Insights Dashboard Template
 * Replicates the Houzez analytics/insights interface
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$userID = get_current_user_id();

// Get date range from query params or default to last 30 days
$start_date = isset( $_GET['start_date'] ) ? sanitize_text_field( $_GET['start_date'] ) : date( 'Y-m-d', strtotime( '-30 days' ) );
$end_date = isset( $_GET['end_date'] ) ? sanitize_text_field( $_GET['end_date'] ) : date( 'Y-m-d' );
$campaign_id = isset( $_GET['campaign_id'] ) ? absint( $_GET['campaign_id'] ) : 0;

// Get analytics data
$analytics = houzez_ads_get_user_analytics( $userID, $start_date, $end_date, $campaign_id );

// Get user campaigns for filter dropdown
$user_campaigns = get_posts( array(
    'post_type' => 'banner_campaign',
    'author' => $userID,
    'posts_per_page' => -1,
    'post_status' => array( 'publish', 'draft', 'pending' ),
    'orderby' => 'title',
    'order' => 'ASC'
) );

$dashboard_campaigns = houzez_ads_get_dashboard_url( 'campaigns' );
?>

<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-md-12">
                <div class="dashboard-content-block-title">
                    <?php _e( 'Campaign Insights', 'houzez-ads-extension' ); ?>
                    <a href="<?php echo esc_url( $dashboard_campaigns ); ?>" class="btn btn-secondary btn-sm float-end">
                        <i class="houzez-icon icon-arrow-left-1 me-2"></i>
                        <?php _e( 'Back to Campaigns', 'houzez-ads-extension' ); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Filters -->
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="houzez-table-filters">
            <form method="GET" id="insights-filter-form">
                <input type="hidden" name="ads_page" value="insights">
                
                <div class="row">
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <div class="form-group">
                            <label for="start_date"><?php _e( 'Start Date', 'houzez-ads-extension' ); ?></label>
                            <input type="date" id="start_date" name="start_date" class="form-control" 
                                   value="<?php echo esc_attr( $start_date ); ?>">
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <div class="form-group">
                            <label for="end_date"><?php _e( 'End Date', 'houzez-ads-extension' ); ?></label>
                            <input type="date" id="end_date" name="end_date" class="form-control" 
                                   value="<?php echo esc_attr( $end_date ); ?>">
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <div class="form-group">
                            <label for="campaign_id"><?php _e( 'Campaign', 'houzez-ads-extension' ); ?></label>
                            <select id="campaign_id" name="campaign_id" class="selectpicker form-control">
                                <option value=""><?php _e( 'All Campaigns', 'houzez-ads-extension' ); ?></option>
                                <?php foreach ( $user_campaigns as $campaign_post ) : ?>
                                    <option value="<?php echo esc_attr( $campaign_post->ID ); ?>" 
                                            <?php selected( $campaign_id, $campaign_post->ID ); ?>>
                                        <?php echo esc_html( $campaign_post->post_title ); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-6 col-sm-12">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="houzez-icon icon-search-1 me-2"></i>
                                <?php _e( 'Apply', 'houzez-ads-extension' ); ?>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="quick-date-filters">
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-date" data-days="7">
                                <?php _e( 'Last 7 Days', 'houzez-ads-extension' ); ?>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-date" data-days="30">
                                <?php _e( 'Last 30 Days', 'houzez-ads-extension' ); ?>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-date" data-days="90">
                                <?php _e( 'Last 90 Days', 'houzez-ads-extension' ); ?>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-date" data-days="365">
                                <?php _e( 'Last Year', 'houzez-ads-extension' ); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Key Metrics Overview -->
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="dashboard-statistic-block">
                    <div class="statistic-block-inner">
                        <div class="statistic-block-number">
                            <?php echo number_format( $analytics['total_impressions'] ); ?>
                        </div>
                        <div class="statistic-block-title">
                            <?php _e( 'Total Impressions', 'houzez-ads-extension' ); ?>
                        </div>
                        <?php if ( $analytics['impressions_change'] !== null ) : ?>
                            <div class="statistic-block-change <?php echo $analytics['impressions_change'] >= 0 ? 'positive' : 'negative'; ?>">
                                <i class="houzez-icon icon-arrow-<?php echo $analytics['impressions_change'] >= 0 ? 'up' : 'down'; ?>-1"></i>
                                <?php echo abs( $analytics['impressions_change'] ); ?>%
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="statistic-block-icon">
                        <i class="houzez-icon icon-eye"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="dashboard-statistic-block">
                    <div class="statistic-block-inner">
                        <div class="statistic-block-number">
                            <?php echo number_format( $analytics['total_clicks'] ); ?>
                        </div>
                        <div class="statistic-block-title">
                            <?php _e( 'Total Clicks', 'houzez-ads-extension' ); ?>
                        </div>
                        <?php if ( $analytics['clicks_change'] !== null ) : ?>
                            <div class="statistic-block-change <?php echo $analytics['clicks_change'] >= 0 ? 'positive' : 'negative'; ?>">
                                <i class="houzez-icon icon-arrow-<?php echo $analytics['clicks_change'] >= 0 ? 'up' : 'down'; ?>-1"></i>
                                <?php echo abs( $analytics['clicks_change'] ); ?>%
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="statistic-block-icon">
                        <i class="houzez-icon icon-cursor-click"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="dashboard-statistic-block">
                    <div class="statistic-block-inner">
                        <div class="statistic-block-number">
                            <?php echo $analytics['ctr']; ?>%
                        </div>
                        <div class="statistic-block-title">
                            <?php _e( 'Click-Through Rate', 'houzez-ads-extension' ); ?>
                        </div>
                        <?php if ( $analytics['ctr_change'] !== null ) : ?>
                            <div class="statistic-block-change <?php echo $analytics['ctr_change'] >= 0 ? 'positive' : 'negative'; ?>">
                                <i class="houzez-icon icon-arrow-<?php echo $analytics['ctr_change'] >= 0 ? 'up' : 'down'; ?>-1"></i>
                                <?php echo abs( $analytics['ctr_change'] ); ?>%
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="statistic-block-icon">
                        <i class="houzez-icon icon-analytics-pie-1"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="dashboard-statistic-block">
                    <div class="statistic-block-inner">
                        <div class="statistic-block-number">
                            <?php echo number_format( $analytics['active_campaigns'] ); ?>
                        </div>
                        <div class="statistic-block-title">
                            <?php _e( 'Active Campaigns', 'houzez-ads-extension' ); ?>
                        </div>
                    </div>
                    <div class="statistic-block-icon">
                        <i class="houzez-icon icon-megaphone"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Chart -->
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-md-12">
                <div class="dashboard-content-block-title">
                    <?php _e( 'Performance Over Time', 'houzez-ads-extension' ); ?>
                </div>
            </div>
        </div>
        
        <div class="dashboard-chart-wrap">
            <canvas id="performanceChart" height="400"></canvas>
        </div>
    </div>
</div>

<!-- Performance by Zone and Type -->
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-lg-6 col-md-12">
                <div class="dashboard-content-block-title">
                    <?php _e( 'Performance by Zone', 'houzez-ads-extension' ); ?>
                </div>
                <div class="dashboard-chart-wrap">
                    <canvas id="zoneChart" height="300"></canvas>
                </div>
            </div>
            
            <div class="col-lg-6 col-md-12">
                <div class="dashboard-content-block-title">
                    <?php _e( 'Performance by Type', 'houzez-ads-extension' ); ?>
                </div>
                <div class="dashboard-chart-wrap">
                    <canvas id="typeChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Performing Campaigns -->
<?php if ( ! empty( $analytics['top_campaigns'] ) ) : ?>
<div class="dashboard-content-block-wrap">
    <div class="dashboard-content-block">
        <div class="row">
            <div class="col-md-12">
                <div class="dashboard-content-block-title">
                    <?php _e( 'Top Performing Campaigns', 'houzez-ads-extension' ); ?>
                </div>
            </div>
        </div>
        
        <div class="dashboard-table-wrap">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th><?php _e( 'Campaign', 'houzez-ads-extension' ); ?></th>
                        <th><?php _e( 'Zone', 'houzez-ads-extension' ); ?></th>
                        <th><?php _e( 'Impressions', 'houzez-ads-extension' ); ?></th>
                        <th><?php _e( 'Clicks', 'houzez-ads-extension' ); ?></th>
                        <th><?php _e( 'CTR', 'houzez-ads-extension' ); ?></th>
                        <th><?php _e( 'Actions', 'houzez-ads-extension' ); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ( $analytics['top_campaigns'] as $top_campaign ) : 
                        $ctr = $top_campaign->impressions > 0 ? round( ( $top_campaign->clicks / $top_campaign->impressions ) * 100, 2 ) : 0;
                        $ad_zones = houzez_ads_get_available_zones();
                    ?>
                        <tr>
                            <td>
                                <strong><?php echo esc_html( $top_campaign->title ); ?></strong>
                            </td>
                            <td>
                                <?php echo esc_html( $ad_zones[ $top_campaign->zone ] ?? $top_campaign->zone ); ?>
                            </td>
                            <td>
                                <?php echo number_format( $top_campaign->impressions ); ?>
                            </td>
                            <td>
                                <?php echo number_format( $top_campaign->clicks ); ?>
                            </td>
                            <td>
                                <span class="badge <?php echo $ctr >= 2 ? 'bg-success' : ( $ctr >= 1 ? 'bg-warning' : 'bg-secondary' ); ?>">
                                    <?php echo $ctr; ?>%
                                </span>
                            </td>
                            <td>
                                <a href="<?php echo esc_url( add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_id' => $top_campaign->ID ), get_permalink() ) ); ?>" 
                                   class="btn btn-sm btn-outline-primary">
                                    <?php _e( 'View Campaign', 'houzez-ads-extension' ); ?>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
jQuery(document).ready(function($) {
    // Quick date filters
    $('.quick-date').on('click', function() {
        var days = $(this).data('days');
        var endDate = new Date();
        var startDate = new Date();
        startDate.setDate(endDate.getDate() - days);
        
        $('#start_date').val(startDate.toISOString().split('T')[0]);
        $('#end_date').val(endDate.toISOString().split('T')[0]);
        
        $('#insights-filter-form').submit();
    });
    
    // Initialize charts if Chart.js is available
    if (typeof Chart !== 'undefined') {
        // Performance over time chart
        var performanceCtx = document.getElementById('performanceChart');
        if (performanceCtx) {
            var performanceData = <?php echo json_encode( $analytics['chart_data'] ); ?>;
            
            new Chart(performanceCtx, {
                type: 'line',
                data: performanceData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });
        }
        
        // Zone performance chart
        var zoneCtx = document.getElementById('zoneChart');
        if (zoneCtx) {
            var zoneData = <?php echo json_encode( $analytics['zone_data'] ); ?>;
            
            new Chart(zoneCtx, {
                type: 'doughnut',
                data: zoneData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        }
        
        // Type performance chart
        var typeCtx = document.getElementById('typeChart');
        if (typeCtx) {
            var typeData = <?php echo json_encode( $analytics['type_data'] ); ?>;
            
            new Chart(typeCtx, {
                type: 'bar',
                data: typeData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
    }
    
    // Initialize selectpicker if available
    if ($.fn.selectpicker) {
        $('.selectpicker').selectpicker({
            style: 'btn-outline-secondary',
            size: 4
        });
    }
});
</script>

<style>
.statistic-block-change {
    font-size: 12px;
    font-weight: 600;
    margin-top: 5px;
}

.statistic-block-change.positive {
    color: #28a745;
}

.statistic-block-change.negative {
    color: #dc3545;
}

.quick-date-filters {
    margin-top: 15px;
}

.quick-date-filters .btn {
    margin-right: 10px;
    margin-bottom: 5px;
}

.dashboard-chart-wrap {
    padding: 20px;
    position: relative;
    height: 400px;
}

.dashboard-chart-wrap canvas {
    max-height: 100%;
}
</style>
