<?php
/**
 * Plugin Name: <PERSON><PERSON>z Ads Extension
 * Plugin URI:  https://example.com/
 * Description: Extends the Real Estate Banner Ads Manager for Ho<PERSON><PERSON> theme with targeted ad campaigns, WooCommerce integration, and Houzez dashboard UI.
 * Version:     1.0.0
 * Author:      Your Name
 * Author URI:  https://example.com/
 * License:     GPL-2.0+
 * License URI: http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain: houzez-ads-extension
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 7.0
 * WC tested up to: 9.9
 * Requires Plugins: woocommerce
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Currently plugin version.
 */
define( 'HOUZEZ_ADS_EXTENSION_VERSION', '1.0.0' );

/**
 * Plugin directory path.
 */
define( 'HOUZEZ_ADS_EXTENSION_PATH', plugin_dir_path( __FILE__ ) );

/**
 * Plugin directory URL.
 */
define( 'HOUZEZ_ADS_EXTENSION_URL', plugin_dir_url( __FILE__ ) );

/**
 * Plugin basename.
 */
define( 'HOUZEZ_ADS_EXTENSION_BASENAME', plugin_basename( __FILE__ ) );

/**
 * The code that runs during plugin activation.
 */
function activate_houzez_ads_extension() {
	require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-ads-activator.php';
	Houzez_Ads_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 */
function deactivate_houzez_ads_extension() {
	require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-ads-deactivator.php';
	Houzez_Ads_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_houzez_ads_extension' );
register_deactivation_hook( __FILE__, 'deactivate_houzez_ads_extension' );

/**
 * Declare WooCommerce HPOS compatibility.
 */
add_action( 'before_woocommerce_init', function() {
	if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
		\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', __FILE__, true );
	}
} );

/**
 * Check for dependencies.
 */
add_action( 'admin_notices', function() {
	// Check for WooCommerce
	if ( ! class_exists( 'WooCommerce' ) ) {
		?>
		<div class="notice notice-warning is-dismissible">
			<p>
				<strong><?php _e( 'Houzez Ads Extension', 'houzez-ads-extension' ); ?></strong>:
				<?php _e( 'WooCommerce is required for the e-commerce functionality. Some features may not work without it.', 'houzez-ads-extension' ); ?>
			</p>
		</div>
		<?php
	}

	// Check for Houzez theme
	$theme = wp_get_theme();
	if ( $theme->get( 'Name' ) !== 'Houzez' && $theme->get( 'Template' ) !== 'houzez' ) {
		?>
		<div class="notice notice-info is-dismissible">
			<p>
				<strong><?php _e( 'Houzez Ads Extension', 'houzez-ads-extension' ); ?></strong>:
				<?php _e( 'This plugin is designed for the Houzez theme. Some features may not work properly with other themes.', 'houzez-ads-extension' ); ?>
			</p>
		</div>
		<?php
	}
} );

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-ads-extension.php';

/**
 * Begins execution of the plugin.
 *
 * Since everything within the plugin is registered via hooks,
 * kicking off the plugin from this point in the file does
 * not affect the page life cycle.
 *
 * @since    1.0.0
 */
function run_houzez_ads_extension() {

	$plugin = new Houzez_Ads_Extension();
	$plugin->run();

}
run_houzez_ads_extension();
