/**
 * <PERSON><PERSON>z Ads Dashboard JavaScript
 * Handles all dashboard functionality including campaign management, analytics, and user interactions
 */

(function($) {
    'use strict';

    // Main dashboard object
    window.houzezAdsDashboard = {
        init: function() {
            this.bindEvents();
            this.initializeComponents();
        },

        bindEvents: function() {
            // Global dashboard events
            $(document).on('click', '.houzez-ads-refresh-data', this.refreshDashboardData);
            $(document).on('submit', '#houzez-ads-campaign-form', this.handleCampaignSubmission);
        },

        initializeComponents: function() {
            // Initialize tooltips
            if (typeof bootstrap !== 'undefined') {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }

            // Initialize selectpicker if available
            if ($.fn.selectpicker) {
                $('.selectpicker').selectpicker({
                    style: 'btn-outline-secondary',
                    size: 4
                });
            }
        },

        refreshDashboardData: function(e) {
            e.preventDefault();
            location.reload();
        },

        handleCampaignSubmission: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $submitBtn = $form.find('button[type="submit"]');
            var originalText = $submitBtn.html();
            
            // Show loading state
            $submitBtn.prop('disabled', true).html('<i class="houzez-icon icon-loader-1 me-2"></i>Processing...');
            
            // Submit form via AJAX
            $.ajax({
                url: houzez_ads_ajax.ajax_url,
                type: 'POST',
                data: new FormData(this),
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        // Redirect to campaigns page with success message
                        window.location.href = response.data.redirect_url || houzez_ads_ajax.campaigns_url;
                    } else {
                        alert(response.data.message || 'Error creating campaign');
                        $submitBtn.prop('disabled', false).html(originalText);
                    }
                },
                error: function() {
                    alert('Error processing request');
                    $submitBtn.prop('disabled', false).html(originalText);
                }
            });
        }
    };

    // Campaign management object
    window.houzezAdsCampaigns = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            // Campaign actions
            $(document).on('click', '.delete-campaign', this.deleteCampaign);
            $(document).on('click', '.duplicate-campaign', this.duplicateCampaign);
            $(document).on('click', '.pause-campaign', this.pauseCampaign);
            $(document).on('click', '.resume-campaign', this.resumeCampaign);
            
            // Bulk actions
            $(document).on('change', '.campaign-bulk-delete', this.updateBulkActions);
            $(document).on('click', '#apply-bulk-action', this.applyBulkAction);
        },

        deleteCampaign: function(e) {
            e.preventDefault();
            
            var campaignId = $(this).data('campaign-id');
            var campaignTitle = $(this).data('campaign-title');
            var $row = $(this).closest('tr');
            
            if (confirm('Are you sure you want to delete the campaign "' + campaignTitle + '"?')) {
                $.ajax({
                    url: houzez_ads_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'houzez_ads_delete_campaign',
                        campaign_id: campaignId,
                        nonce: $(this).data('nonce')
                    },
                    beforeSend: function() {
                        $row.css('opacity', '0.5');
                    },
                    success: function(response) {
                        if (response.success) {
                            $row.fadeOut(300, function() {
                                $(this).remove();
                                houzezAdsCampaigns.updateVisibleCount();
                            });
                        } else {
                            alert(response.data.message || 'Failed to delete campaign');
                            $row.css('opacity', '1');
                        }
                    },
                    error: function() {
                        alert('Error deleting campaign');
                        $row.css('opacity', '1');
                    }
                });
            }
        },

        duplicateCampaign: function(e) {
            e.preventDefault();
            
            var campaignId = $(this).data('campaign-id');
            var $button = $(this);
            var originalText = $button.text();
            
            $button.text('Duplicating...');
            
            $.ajax({
                url: houzez_ads_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'houzez_ads_duplicate_campaign',
                    campaign_id: campaignId,
                    nonce: $(this).data('nonce')
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.data.message || 'Failed to duplicate campaign');
                    }
                },
                error: function() {
                    alert('Error duplicating campaign');
                },
                complete: function() {
                    $button.text(originalText);
                }
            });
        },

        pauseCampaign: function(e) {
            e.preventDefault();
            
            var campaignId = $(this).data('campaign-id');
            var $button = $(this);
            
            $button.text('Pausing...');
            
            $.ajax({
                url: houzez_ads_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'houzez_ads_toggle_campaign',
                    campaign_id: campaignId,
                    toggle_action: 'pause',
                    nonce: $(this).data('nonce')
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.data.message || 'Failed to pause campaign');
                    }
                },
                error: function() {
                    alert('Error pausing campaign');
                }
            });
        },

        resumeCampaign: function(e) {
            e.preventDefault();
            
            var campaignId = $(this).data('campaign-id');
            var $button = $(this);
            
            $button.text('Resuming...');
            
            $.ajax({
                url: houzez_ads_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'houzez_ads_toggle_campaign',
                    campaign_id: campaignId,
                    toggle_action: 'resume',
                    nonce: $(this).data('nonce')
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.data.message || 'Failed to resume campaign');
                    }
                },
                error: function() {
                    alert('Error resuming campaign');
                }
            });
        },

        updateBulkActions: function() {
            var selectedCount = $('.campaign-bulk-delete:checked').length;
            $('.selected-count').text(selectedCount);
            
            if (selectedCount > 0) {
                $('.bulk-actions-wrap').removeClass('d-none');
            } else {
                $('.bulk-actions-wrap').addClass('d-none');
            }
            
            // Update select all checkbox
            var totalVisible = $('.campaign-bulk-delete:visible').length;
            var selectedVisible = $('.campaign-bulk-delete:visible:checked').length;
            $('#select-all-campaigns').prop('checked', totalVisible > 0 && selectedVisible === totalVisible);
        },

        applyBulkAction: function(e) {
            e.preventDefault();
            
            var action = $('#bulk-action-select').val();
            var selectedCampaigns = [];
            
            $('.campaign-bulk-delete:checked').each(function() {
                selectedCampaigns.push($(this).val());
            });

            if (!action || selectedCampaigns.length === 0) {
                alert('Please select an action and campaigns');
                return;
            }

            var confirmMessage = '';
            switch (action) {
                case 'delete':
                    confirmMessage = 'Are you sure you want to delete the selected campaigns?';
                    break;
                case 'pause':
                    confirmMessage = 'Are you sure you want to pause the selected campaigns?';
                    break;
                case 'resume':
                    confirmMessage = 'Are you sure you want to resume the selected campaigns?';
                    break;
            }

            if (confirm(confirmMessage)) {
                $.ajax({
                    url: houzez_ads_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'houzez_ads_bulk_action',
                        bulk_action: action,
                        campaign_ids: selectedCampaigns,
                        nonce: houzez_ads_ajax.nonce
                    },
                    beforeSend: function() {
                        $('#apply-bulk-action').prop('disabled', true).text('Processing...');
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.data.message || 'Failed to perform bulk action');
                        }
                    },
                    error: function() {
                        alert('Error performing bulk action');
                    },
                    complete: function() {
                        $('#apply-bulk-action').prop('disabled', false).text('Apply');
                    }
                });
            }
        },

        applyFilters: function() {
            var zone = $('#campaign-zone-filter').val();
            var type = $('#campaign-type-filter').val();
            var search = $('#campaign-search').val().toLowerCase();

            $('.campaign-table tbody tr').each(function() {
                var $row = $(this);
                var rowZone = $row.data('zone');
                var rowType = $row.data('type');
                var rowText = $row.text().toLowerCase();

                var showRow = true;

                if (zone && rowZone !== zone) showRow = false;
                if (type && rowType !== type) showRow = false;
                if (search && rowText.indexOf(search) === -1) showRow = false;

                $row.toggle(showRow);
            });

            this.updateVisibleCount();
        },

        updateVisibleCount: function() {
            var visibleCount = $('.campaign-table tbody tr:visible').length;
            $('.visible-count').text(visibleCount);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize main dashboard
        if (typeof houzezAdsDashboard !== 'undefined') {
            houzezAdsDashboard.init();
        }
        
        // Initialize campaigns management
        if (typeof houzezAdsCampaigns !== 'undefined') {
            houzezAdsCampaigns.init();
        }
    });

})(jQuery);
