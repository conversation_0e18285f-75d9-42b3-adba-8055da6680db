<?php
/**
 * Ads Dashboard Menu Template (replicates Houzez dashboard menu structure)
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$userID = get_current_user_id();
$dashboard_link = houzez_ads_get_dashboard_url();

// Get current page/section
$current_page = isset( $_GET['dashboard_page'] ) ? sanitize_text_field( $_GET['dashboard_page'] ) : 'campaigns';
$campaign_status = isset( $_GET['campaign_status'] ) ? sanitize_text_field( $_GET['campaign_status'] ) : '';

// Build menu URLs
$campaigns_link = add_query_arg( 'dashboard_page', 'campaigns', $dashboard_link );
$create_campaign_link = add_query_arg( 'dashboard_page', 'create_campaign', $dashboard_link );
$insights_link = add_query_arg( 'dashboard_page', 'insights', $dashboard_link );
$credits_link = add_query_arg( 'dashboard_page', 'credits', $dashboard_link );

// Campaign status links
$all_campaigns = add_query_arg( array( 'dashboard_page' => 'campaigns', 'campaign_status' => 'all' ), $dashboard_link );
$approved_campaigns = add_query_arg( array( 'dashboard_page' => 'campaigns', 'campaign_status' => 'approved' ), $dashboard_link );
$pending_campaigns = add_query_arg( array( 'dashboard_page' => 'campaigns', 'campaign_status' => 'pending' ), $dashboard_link );
$rejected_campaigns = add_query_arg( array( 'dashboard_page' => 'campaigns', 'campaign_status' => 'rejected' ), $dashboard_link );
$expired_campaigns = add_query_arg( array( 'dashboard_page' => 'campaigns', 'campaign_status' => 'expired' ), $dashboard_link );

// Set active states
$ac_campaigns = $ac_create = $ac_insights = $ac_credits = '';
$ac_all = $ac_approved = $ac_pending = $ac_rejected = $ac_expired = '';
$parent_campaigns = '';

if ( $current_page === 'campaigns' ) {
	$ac_campaigns = 'active';
	$parent_campaigns = 'side-menu-parent-selected';
	
	// Set campaign status active states
	switch ( $campaign_status ) {
		case 'approved':
			$ac_approved = 'class=active';
			break;
		case 'pending':
			$ac_pending = 'class=active';
			break;
		case 'rejected':
			$ac_rejected = 'class=active';
			break;
		case 'expired':
			$ac_expired = 'class=active';
			break;
		default:
			$ac_all = 'class=active';
	}
} elseif ( $current_page === 'create_campaign' ) {
	$ac_create = 'active';
} elseif ( $current_page === 'insights' ) {
	$ac_insights = 'active';
} elseif ( $current_page === 'credits' ) {
	$ac_credits = 'active';
}

// Get campaign counts
$campaign_counts = houzez_ads_get_user_campaign_counts( $userID );
$user_credits = houzez_ads_get_user_credits( $userID );

// Check user permissions
$can_create_campaigns = houzez_ads_user_can_create_campaigns();
?>

<div class="sidebar-nav">
	<?php if ( $can_create_campaigns ) : ?>
		<!-- Overview Section -->
		<div class="nav-box">
			<h5><?php _e( 'Ad Campaign Overview', 'houzez-ads-extension' ); ?></h5>
			<ul>
				<li>
					<a href="<?php echo esc_url( $insights_link ); ?>" class="<?php echo esc_attr( $ac_insights ); ?>">
						<i class="houzez-icon icon-analytics-pie-1"></i>
						<span><?php _e( 'Campaign Insights', 'houzez-ads-extension' ); ?></span>
					</a>
				</li>
				<li>
					<a href="<?php echo esc_url( $credits_link ); ?>" class="<?php echo esc_attr( $ac_credits ); ?>">
						<i class="houzez-icon icon-accounting-document"></i>
						<span><?php _e( 'Credits & Billing', 'houzez-ads-extension' ); ?></span>
						<span class="badge bg-primary ms-2"><?php echo number_format( $user_credits ); ?></span>
					</a>
				</li>
			</ul>
		</div>

		<!-- Campaign Management Section -->
		<div class="nav-box">
			<h5><?php _e( 'Campaign Management', 'houzez-ads-extension' ); ?></h5>
			<ul>
				<li>
					<a href="<?php echo esc_url( $campaigns_link ); ?>" class="<?php echo esc_attr( $ac_campaigns ); ?>">
						<i class="houzez-icon icon-megaphone"></i>
						<span><?php _e( 'My Campaigns', 'houzez-ads-extension' ); ?></span>
						<span class="badge bg-secondary ms-2"><?php echo $campaign_counts['total']; ?></span>
					</a>
					
					<!-- Campaign Status Submenu (similar to Houzez property status submenu) -->
					<?php if ( $current_page === 'campaigns' ) : ?>
						<ul class="sub-menu">
							<li>
								<a href="<?php echo esc_url( $all_campaigns ); ?>" <?php echo $ac_all; ?>>
									<span><?php _e( 'All Campaigns', 'houzez-ads-extension' ); ?></span>
									<span class="count">(<?php echo $campaign_counts['total']; ?>)</span>
								</a>
							</li>
							<li>
								<a href="<?php echo esc_url( $approved_campaigns ); ?>" <?php echo $ac_approved; ?>>
									<span><?php _e( 'Approved', 'houzez-ads-extension' ); ?></span>
									<span class="count">(<?php echo $campaign_counts['approved']; ?>)</span>
								</a>
							</li>
							<li>
								<a href="<?php echo esc_url( $pending_campaigns ); ?>" <?php echo $ac_pending; ?>>
									<span><?php _e( 'Pending', 'houzez-ads-extension' ); ?></span>
									<span class="count">(<?php echo $campaign_counts['pending']; ?>)</span>
								</a>
							</li>
							<li>
								<a href="<?php echo esc_url( $rejected_campaigns ); ?>" <?php echo $ac_rejected; ?>>
									<span><?php _e( 'Rejected', 'houzez-ads-extension' ); ?></span>
									<span class="count">(<?php echo $campaign_counts['rejected']; ?>)</span>
								</a>
							</li>
							<li>
								<a href="<?php echo esc_url( $expired_campaigns ); ?>" <?php echo $ac_expired; ?>>
									<span><?php _e( 'Expired', 'houzez-ads-extension' ); ?></span>
									<span class="count">(<?php echo $campaign_counts['expired']; ?>)</span>
								</a>
							</li>
						</ul>
					<?php endif; ?>
				</li>
				
				<li>
					<a href="<?php echo esc_url( $create_campaign_link ); ?>" class="<?php echo esc_attr( $ac_create ); ?>">
						<i class="houzez-icon icon-add-circle"></i>
						<span><?php _e( 'Create Campaign', 'houzez-ads-extension' ); ?></span>
					</a>
				</li>
			</ul>
		</div>

		<!-- Quick Stats Section -->
		<div class="nav-box">
			<h5><?php _e( 'Quick Stats', 'houzez-ads-extension' ); ?></h5>
			<div class="dashboard-quick-stats">
				<div class="stat-item">
					<div class="stat-number text-success"><?php echo $campaign_counts['approved']; ?></div>
					<div class="stat-label"><?php _e( 'Active Campaigns', 'houzez-ads-extension' ); ?></div>
				</div>
				<div class="stat-item">
					<div class="stat-number text-warning"><?php echo $campaign_counts['pending']; ?></div>
					<div class="stat-label"><?php _e( 'Pending Review', 'houzez-ads-extension' ); ?></div>
				</div>
				<div class="stat-item">
					<div class="stat-number text-primary"><?php echo number_format( $user_credits ); ?></div>
					<div class="stat-label"><?php _e( 'Available Credits', 'houzez-ads-extension' ); ?></div>
				</div>
			</div>
		</div>

		<!-- Help & Support Section -->
		<div class="nav-box">
			<h5><?php _e( 'Help & Support', 'houzez-ads-extension' ); ?></h5>
			<ul>
				<li>
					<a href="#" class="houzez-ads-help-modal" data-bs-toggle="modal" data-bs-target="#adsHelpModal">
						<i class="houzez-icon icon-single-neutral-question"></i>
						<span><?php _e( 'Campaign Guidelines', 'houzez-ads-extension' ); ?></span>
					</a>
				</li>
				<li>
					<a href="#" class="houzez-ads-pricing-modal" data-bs-toggle="modal" data-bs-target="#adsPricingModal">
						<i class="houzez-icon icon-task-list-text-1"></i>
						<span><?php _e( 'Pricing Information', 'houzez-ads-extension' ); ?></span>
					</a>
				</li>
			</ul>
		</div>

	<?php else : ?>
		<!-- Limited access for users without campaign permissions -->
		<div class="nav-box">
			<div class="alert alert-info">
				<h6><?php _e( 'Ad Campaigns', 'houzez-ads-extension' ); ?></h6>
				<p class="mb-0"><?php _e( 'Upgrade your account to create and manage ad campaigns.', 'houzez-ads-extension' ); ?></p>
				<a href="#" class="btn btn-primary btn-sm mt-2"><?php _e( 'Upgrade Now', 'houzez-ads-extension' ); ?></a>
			</div>
		</div>
	<?php endif; ?>
</div>

<!-- Help Modal -->
<div class="modal fade" id="adsHelpModal" tabindex="-1" aria-labelledby="adsHelpModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="adsHelpModalLabel"><?php _e( 'Campaign Guidelines', 'houzez-ads-extension' ); ?></h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<h6><?php _e( 'Creating Effective Campaigns', 'houzez-ads-extension' ); ?></h6>
				<ul>
					<li><?php _e( 'Use high-quality images with clear, professional appearance', 'houzez-ads-extension' ); ?></li>
					<li><?php _e( 'Write compelling headlines that grab attention', 'houzez-ads-extension' ); ?></li>
					<li><?php _e( 'Target the right audience for your property type', 'houzez-ads-extension' ); ?></li>
					<li><?php _e( 'Choose appropriate ad zones for maximum visibility', 'houzez-ads-extension' ); ?></li>
				</ul>
				
				<h6 class="mt-4"><?php _e( 'Approval Process', 'houzez-ads-extension' ); ?></h6>
				<p><?php _e( 'All campaigns are reviewed within 24 hours. Campaigns must comply with our advertising guidelines and community standards.', 'houzez-ads-extension' ); ?></p>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e( 'Close', 'houzez-ads-extension' ); ?></button>
			</div>
		</div>
	</div>
</div>

<!-- Pricing Modal -->
<div class="modal fade" id="adsPricingModal" tabindex="-1" aria-labelledby="adsPricingModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="adsPricingModalLabel"><?php _e( 'Pricing Information', 'houzez-ads-extension' ); ?></h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<?php echo do_shortcode( '[houzez_ads_pricing_table]' ); ?>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e( 'Close', 'houzez-ads-extension' ); ?></button>
				<a href="<?php echo esc_url( $create_campaign_link ); ?>" class="btn btn-primary"><?php _e( 'Create Campaign', 'houzez-ads-extension' ); ?></a>
			</div>
		</div>
	</div>
</div>

<style>
.dashboard-quick-stats {
	padding: 15px 0;
}

.dashboard-quick-stats .stat-item {
	text-align: center;
	margin-bottom: 15px;
	padding: 10px;
	background: #f8f9fa;
	border-radius: 6px;
}

.dashboard-quick-stats .stat-number {
	font-size: 24px;
	font-weight: bold;
	display: block;
}

.dashboard-quick-stats .stat-label {
	font-size: 12px;
	color: #6c757d;
	margin-top: 5px;
}

.nav-box .sub-menu {
	margin-left: 20px;
	margin-top: 10px;
}

.nav-box .sub-menu li {
	margin-bottom: 5px;
}

.nav-box .sub-menu a {
	font-size: 14px;
	padding: 5px 10px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.nav-box .sub-menu .count {
	font-size: 12px;
	color: #6c757d;
}
</style>
