<?php
/**
 * Campaign insights dashboard template using Ho<PERSON><PERSON> insights UI
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$current_user_id = get_current_user_id();

// Get date filter parameters
$date_filter = isset( $_GET['date_filter'] ) ? sanitize_text_field( $_GET['date_filter'] ) : '30_days';
$campaign_id = isset( $_GET['campaign_id'] ) ? absint( $_GET['campaign_id'] ) : 0;

// Get user's campaigns for filter dropdown
$user_campaigns = get_posts( array(
	'post_type' => 'banner_campaign',
	'author' => $current_user_id,
	'posts_per_page' => -1,
	'post_status' => array( 'publish', 'draft', 'pending' ),
	'orderby' => 'date',
	'order' => 'DESC'
) );

// Calculate date range based on filter
$end_date = current_time( 'Y-m-d' );
switch ( $date_filter ) {
	case '7_days':
		$start_date = date( 'Y-m-d', strtotime( '-7 days' ) );
		$period_label = __( 'Last 7 Days', 'houzez-ads-extension' );
		break;
	case '30_days':
		$start_date = date( 'Y-m-d', strtotime( '-30 days' ) );
		$period_label = __( 'Last 30 Days', 'houzez-ads-extension' );
		break;
	case '90_days':
		$start_date = date( 'Y-m-d', strtotime( '-90 days' ) );
		$period_label = __( 'Last 90 Days', 'houzez-ads-extension' );
		break;
	default:
		$start_date = date( 'Y-m-d', strtotime( '-30 days' ) );
		$period_label = __( 'Last 30 Days', 'houzez-ads-extension' );
}

// Get analytics data
$analytics_data = houzez_ads_get_user_analytics( $current_user_id, $start_date, $end_date, $campaign_id );
?>

<div class="dashboard-content-block-wrap">
	<!-- Header with filters (similar to Houzez insights) -->
	<div class="dashboard-content-block">
		<div class="d-flex align-items-center justify-content-between">
			<div class="dashboard-content-block-title">
				<?php _e( 'Campaign Insights', 'houzez-ads-extension' ); ?>
			</div>
			<div class="insights-filters d-flex gap-3">
				<!-- Campaign Filter -->
				<select id="campaign-filter" class="selectpicker form-control" title="<?php _e( 'All Campaigns', 'houzez-ads-extension' ); ?>">
					<option value="0"><?php _e( 'All Campaigns', 'houzez-ads-extension' ); ?></option>
					<?php foreach ( $user_campaigns as $campaign_post ) : 
						$campaign = new Houzez_Banner_Campaign( $campaign_post );
					?>
						<option value="<?php echo esc_attr( $campaign->id ); ?>" <?php selected( $campaign_id, $campaign->id ); ?>>
							<?php echo esc_html( $campaign->title ); ?>
						</option>
					<?php endforeach; ?>
				</select>

				<!-- Date Filter -->
				<select id="date-filter" class="selectpicker form-control">
					<option value="7_days" <?php selected( $date_filter, '7_days' ); ?>><?php _e( 'Last 7 Days', 'houzez-ads-extension' ); ?></option>
					<option value="30_days" <?php selected( $date_filter, '30_days' ); ?>><?php _e( 'Last 30 Days', 'houzez-ads-extension' ); ?></option>
					<option value="90_days" <?php selected( $date_filter, '90_days' ); ?>><?php _e( 'Last 90 Days', 'houzez-ads-extension' ); ?></option>
				</select>
			</div>
		</div>
	</div>

	<!-- Stats Overview (similar to Houzez insights stats) -->
	<div class="dashboard-content-block">
		<div class="row g-4">
			<div class="col-lg-3 col-md-6">
				<div class="block-wrap">
					<div class="card-body">
						<div class="stats-box bg-light rounded p-3">
							<p class="small text-muted"><?php _e( 'Total Impressions', 'houzez-ads-extension' ); ?></p>
							<div class="d-flex align-items-baseline gap-2">
								<h3 class="mb-0"><?php echo number_format_i18n( $analytics_data['total_impressions'] ); ?></h3>
								<?php if ( $analytics_data['impressions_change'] !== null ) : ?>
									<span class="badge <?php echo $analytics_data['impressions_change'] >= 0 ? 'bg-success' : 'bg-danger'; ?>">
										<?php echo $analytics_data['impressions_change'] >= 0 ? '+' : ''; ?><?php echo number_format( $analytics_data['impressions_change'], 1 ); ?>%
									</span>
								<?php endif; ?>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-3 col-md-6">
				<div class="block-wrap">
					<div class="card-body">
						<div class="stats-box bg-light rounded p-3">
							<p class="small text-muted"><?php _e( 'Total Clicks', 'houzez-ads-extension' ); ?></p>
							<div class="d-flex align-items-baseline gap-2">
								<h3 class="mb-0"><?php echo number_format_i18n( $analytics_data['total_clicks'] ); ?></h3>
								<?php if ( $analytics_data['clicks_change'] !== null ) : ?>
									<span class="badge <?php echo $analytics_data['clicks_change'] >= 0 ? 'bg-success' : 'bg-danger'; ?>">
										<?php echo $analytics_data['clicks_change'] >= 0 ? '+' : ''; ?><?php echo number_format( $analytics_data['clicks_change'], 1 ); ?>%
									</span>
								<?php endif; ?>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-3 col-md-6">
				<div class="block-wrap">
					<div class="card-body">
						<div class="stats-box bg-light rounded p-3">
							<p class="small text-muted"><?php _e( 'Click-Through Rate', 'houzez-ads-extension' ); ?></p>
							<div class="d-flex align-items-baseline gap-2">
								<h3 class="mb-0"><?php echo number_format( $analytics_data['ctr'], 2 ); ?>%</h3>
								<?php if ( $analytics_data['ctr_change'] !== null ) : ?>
									<span class="badge <?php echo $analytics_data['ctr_change'] >= 0 ? 'bg-success' : 'bg-danger'; ?>">
										<?php echo $analytics_data['ctr_change'] >= 0 ? '+' : ''; ?><?php echo number_format( $analytics_data['ctr_change'], 1 ); ?>%
									</span>
								<?php endif; ?>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-3 col-md-6">
				<div class="block-wrap">
					<div class="card-body">
						<div class="stats-box bg-light rounded p-3">
							<p class="small text-muted"><?php _e( 'Active Campaigns', 'houzez-ads-extension' ); ?></p>
							<div class="d-flex align-items-baseline gap-2">
								<h3 class="mb-0"><?php echo number_format_i18n( $analytics_data['active_campaigns'] ); ?></h3>
								<span class="badge bg-info"><?php _e( 'Running', 'houzez-ads-extension' ); ?></span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Charts Section (similar to Houzez insights charts) -->
	<div class="houzez-data-content">
		<div class="row">
			<!-- Main Performance Chart -->
			<div class="col-lg-8">
				<div class="block-wrap">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center mb-4">
							<h5 class="card-title mb-0"><?php _e( 'Performance Overview', 'houzez-ads-extension' ); ?></h5>
							<small class="text-muted"><?php echo esc_html( $period_label ); ?></small>
						</div>
						<div id="campaign-performance-chart" style="height: 300px;"></div>
					</div>
				</div>
			</div>

			<!-- Top Performing Campaigns -->
			<div class="col-lg-4">
				<div class="block-wrap">
					<div class="card-body">
						<h5 class="card-title mb-4"><?php _e( 'Top Performing Campaigns', 'houzez-ads-extension' ); ?></h5>
						<div class="top-campaigns-list">
							<?php if ( ! empty( $analytics_data['top_campaigns'] ) ) : ?>
								<?php foreach ( $analytics_data['top_campaigns'] as $top_campaign ) : ?>
									<div class="d-flex justify-content-between align-items-center mb-3 p-2 bg-light rounded">
										<div>
											<strong><?php echo esc_html( $top_campaign['title'] ); ?></strong>
											<br>
											<small class="text-muted"><?php echo esc_html( $top_campaign['zone'] ); ?></small>
										</div>
										<div class="text-end">
											<div class="fw-bold"><?php echo number_format( $top_campaign['clicks'] ); ?></div>
											<small class="text-muted"><?php _e( 'clicks', 'houzez-ads-extension' ); ?></small>
										</div>
									</div>
								<?php endforeach; ?>
							<?php else : ?>
								<p class="text-muted text-center"><?php _e( 'No campaign data available for this period.', 'houzez-ads-extension' ); ?></p>
							<?php endif; ?>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Zone Performance -->
		<div class="row mt-4">
			<div class="col-lg-6">
				<div class="block-wrap">
					<div class="card-body">
						<h5 class="card-title mb-4"><?php _e( 'Performance by Zone', 'houzez-ads-extension' ); ?></h5>
						<div id="zone-performance-chart" style="height: 250px;"></div>
					</div>
				</div>
			</div>

			<div class="col-lg-6">
				<div class="block-wrap">
					<div class="card-body">
						<h5 class="card-title mb-4"><?php _e( 'Campaign Types Performance', 'houzez-ads-extension' ); ?></h5>
						<div id="type-performance-chart" style="height: 250px;"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
jQuery(document).ready(function($) {
	// Handle filter changes
	$('#campaign-filter, #date-filter').on('change', function() {
		var campaignId = $('#campaign-filter').val();
		var dateFilter = $('#date-filter').val();
		
		var url = new URL(window.location);
		url.searchParams.set('campaign_id', campaignId);
		url.searchParams.set('date_filter', dateFilter);
		
		window.location.href = url.toString();
	});

	// Initialize charts if Chart.js is available
	if (typeof Chart !== 'undefined') {
		initializeCharts();
	}
});

function initializeCharts() {
	// Performance chart data from PHP
	var performanceData = <?php echo json_encode( $analytics_data['chart_data'] ); ?>;
	var zoneData = <?php echo json_encode( $analytics_data['zone_data'] ); ?>;
	var typeData = <?php echo json_encode( $analytics_data['type_data'] ); ?>;

	// Main performance chart
	var ctx1 = document.getElementById('campaign-performance-chart');
	if (ctx1) {
		new Chart(ctx1, {
			type: 'line',
			data: performanceData,
			options: {
				responsive: true,
				maintainAspectRatio: false,
				scales: {
					y: {
						beginAtZero: true
					}
				}
			}
		});
	}

	// Zone performance chart
	var ctx2 = document.getElementById('zone-performance-chart');
	if (ctx2) {
		new Chart(ctx2, {
			type: 'doughnut',
			data: zoneData,
			options: {
				responsive: true,
				maintainAspectRatio: false
			}
		});
	}

	// Type performance chart
	var ctx3 = document.getElementById('type-performance-chart');
	if (ctx3) {
		new Chart(ctx3, {
			type: 'bar',
			data: typeData,
			options: {
				responsive: true,
				maintainAspectRatio: false,
				scales: {
					y: {
						beginAtZero: true
					}
				}
			}
		});
	}
}
</script>
