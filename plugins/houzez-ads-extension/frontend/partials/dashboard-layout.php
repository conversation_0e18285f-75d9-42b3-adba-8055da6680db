<?php
/**
 * Main Dashboard Layout Template (replicates Houzez dashboard structure)
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// Get current page
$current_page = isset( $_GET['dashboard_page'] ) ? sanitize_text_field( $_GET['dashboard_page'] ) : 'campaigns';

// Check user permissions
if ( ! houzez_ads_user_can_create_campaigns() ) {
	?>
	<div class="dashboard-content-block-wrap">
		<div class="dashboard-content-block">
			<div class="alert alert-warning">
				<h5><?php _e( 'Access Restricted', 'houzez-ads-extension' ); ?></h5>
				<p><?php _e( 'You need to upgrade your account to access ad campaign features.', 'houzez-ads-extension' ); ?></p>
				<a href="#" class="btn btn-primary"><?php _e( 'Upgrade Account', 'houzez-ads-extension' ); ?></a>
			</div>
		</div>
	</div>
	<?php
	return;
}

// Get user data for dashboard
$current_user_id = get_current_user_id();
$user_credits = houzez_ads_get_user_credits( $current_user_id );
$campaign_counts = houzez_ads_get_user_campaign_counts( $current_user_id );
?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title><?php _e( 'Ad Campaign Dashboard', 'houzez-ads-extension' ); ?> - <?php bloginfo( 'name' ); ?></title>
	<?php wp_head(); ?>
</head>

<body class="houzez-dashboard-body">
	<div class="dashboard-wrap">
		<!-- Dashboard Sidebar -->
		<div class="dashboard-sidebar-wrap">
			<?php include HOUZEZ_ADS_EXTENSION_PATH . 'frontend/partials/dashboard-menu.php'; ?>
		</div>

		<!-- Dashboard Main Content -->
		<div class="dashboard-main-wrap">
			<!-- Dashboard Header/Topbar -->
			<div class="dashboard-header">
				<div class="d-flex align-items-center justify-content-between">
					<div class="dashboard-header-left">
						<button type="button" class="btn btn-link dashboard-sidebar-toggle d-lg-none">
							<i class="houzez-icon icon-navigation-menu"></i>
						</button>
						<h1 class="dashboard-page-title">
							<?php
							switch ( $current_page ) {
								case 'campaigns':
									_e( 'My Campaigns', 'houzez-ads-extension' );
									break;
								case 'create_campaign':
									_e( 'Create Campaign', 'houzez-ads-extension' );
									break;
								case 'insights':
									_e( 'Campaign Insights', 'houzez-ads-extension' );
									break;
								case 'credits':
									_e( 'Credits & Billing', 'houzez-ads-extension' );
									break;
								default:
									_e( 'Ad Campaigns', 'houzez-ads-extension' );
							}
							?>
						</h1>
					</div>
					
					<div class="dashboard-header-right">
						<!-- Quick Stats -->
						<div class="dashboard-quick-info d-none d-md-flex align-items-center gap-4">
							<div class="quick-stat">
								<span class="stat-label"><?php _e( 'Credits', 'houzez-ads-extension' ); ?></span>
								<span class="stat-value text-primary"><?php echo number_format( $user_credits ); ?></span>
							</div>
							<div class="quick-stat">
								<span class="stat-label"><?php _e( 'Active', 'houzez-ads-extension' ); ?></span>
								<span class="stat-value text-success"><?php echo $campaign_counts['approved']; ?></span>
							</div>
							<div class="quick-stat">
								<span class="stat-label"><?php _e( 'Pending', 'houzez-ads-extension' ); ?></span>
								<span class="stat-value text-warning"><?php echo $campaign_counts['pending']; ?></span>
							</div>
						</div>

						<!-- User Menu -->
						<div class="dropdown">
							<?php
							$current_user = wp_get_current_user();
							$user_display_name = $current_user->display_name;
							$user_custom_picture = houzez_get_profile_pic( $current_user_id );
							?>
							<button class="btn btn-link dropdown-toggle user-dropdown" type="button" data-bs-toggle="dropdown">
								<img src="<?php echo esc_url( $user_custom_picture ); ?>" alt="<?php echo esc_attr( $user_display_name ); ?>" class="user-avatar">
								<span class="user-name d-none d-md-inline"><?php echo esc_html( $user_display_name ); ?></span>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<a class="dropdown-item" href="<?php echo esc_url( home_url() ); ?>">
										<i class="houzez-icon icon-share-2 me-2"></i>
										<?php _e( 'Visit Site', 'houzez-ads-extension' ); ?>
									</a>
								</li>
								<li>
									<a class="dropdown-item" href="<?php echo esc_url( houzez_get_template_link_2( 'template/user_dashboard_profile.php' ) ); ?>">
										<i class="houzez-icon icon-single-neutral-circle me-2"></i>
										<?php _e( 'Profile', 'houzez-ads-extension' ); ?>
									</a>
								</li>
								<li><hr class="dropdown-divider"></li>
								<li>
									<a class="dropdown-item" href="<?php echo wp_logout_url( home_url() ); ?>">
										<i class="houzez-icon icon-logout-1 me-2"></i>
										<?php _e( 'Logout', 'houzez-ads-extension' ); ?>
									</a>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>

			<!-- Dashboard Content Area -->
			<div class="dashboard-content">
				<?php
				// Display appropriate content based on current page
				switch ( $current_page ) {
					case 'campaigns':
						echo do_shortcode( '[houzez_ads_dashboard]' );
						break;
					case 'create_campaign':
						echo do_shortcode( '[houzez_ads_upload]' );
						break;
					case 'insights':
						echo do_shortcode( '[houzez_ads_insights]' );
						break;
					case 'credits':
						echo do_shortcode( '[houzez_ads_credits]' );
						break;
					default:
						echo do_shortcode( '[houzez_ads_dashboard]' );
				}
				?>
			</div>

			<!-- Dashboard Footer -->
			<div class="dashboard-footer">
				<div class="d-flex align-items-center justify-content-between">
					<div class="footer-left">
						<p class="mb-0 text-muted">
							<?php printf( __( '© %s %s. All rights reserved.', 'houzez-ads-extension' ), date( 'Y' ), get_bloginfo( 'name' ) ); ?>
						</p>
					</div>
					<div class="footer-right">
						<div class="footer-links">
							<a href="#" class="text-muted me-3"><?php _e( 'Help', 'houzez-ads-extension' ); ?></a>
							<a href="#" class="text-muted me-3"><?php _e( 'Support', 'houzez-ads-extension' ); ?></a>
							<a href="#" class="text-muted"><?php _e( 'Terms', 'houzez-ads-extension' ); ?></a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Loading Overlay -->
	<div id="houzez-ads-loading-overlay" class="loading-overlay" style="display: none;">
		<div class="loading-spinner">
			<div class="spinner-border text-primary" role="status">
				<span class="visually-hidden"><?php _e( 'Loading...', 'houzez-ads-extension' ); ?></span>
			</div>
		</div>
	</div>

	<?php wp_footer(); ?>

	<style>
	.houzez-dashboard-body {
		font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
		background-color: #f8f9fa;
		margin: 0;
		padding: 0;
	}

	.dashboard-wrap {
		display: flex;
		min-height: 100vh;
	}

	.dashboard-sidebar-wrap {
		width: 280px;
		background: #fff;
		border-right: 1px solid #e9ecef;
		position: fixed;
		height: 100vh;
		overflow-y: auto;
		z-index: 1000;
	}

	.dashboard-main-wrap {
		flex: 1;
		margin-left: 280px;
		display: flex;
		flex-direction: column;
	}

	.dashboard-header {
		background: #fff;
		border-bottom: 1px solid #e9ecef;
		padding: 1rem 2rem;
		position: sticky;
		top: 0;
		z-index: 999;
	}

	.dashboard-page-title {
		font-size: 1.5rem;
		font-weight: 600;
		margin: 0;
		color: #2c3e50;
	}

	.dashboard-quick-info {
		gap: 2rem;
	}

	.quick-stat {
		text-align: center;
	}

	.quick-stat .stat-label {
		display: block;
		font-size: 0.75rem;
		color: #6c757d;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.quick-stat .stat-value {
		display: block;
		font-size: 1.25rem;
		font-weight: 600;
		margin-top: 0.25rem;
	}

	.user-dropdown {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		text-decoration: none;
		color: inherit;
		border: none;
		background: none;
	}

	.user-avatar {
		width: 32px;
		height: 32px;
		border-radius: 50%;
		object-fit: cover;
	}

	.dashboard-content {
		flex: 1;
		padding: 2rem;
		overflow-y: auto;
	}

	.dashboard-footer {
		background: #fff;
		border-top: 1px solid #e9ecef;
		padding: 1rem 2rem;
		margin-top: auto;
	}

	.footer-links a {
		text-decoration: none;
		font-size: 0.875rem;
	}

	.footer-links a:hover {
		text-decoration: underline;
	}

	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(255, 255, 255, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.loading-spinner {
		text-align: center;
	}

	/* Responsive Design */
	@media (max-width: 991.98px) {
		.dashboard-sidebar-wrap {
			transform: translateX(-100%);
			transition: transform 0.3s ease;
		}

		.dashboard-sidebar-wrap.show {
			transform: translateX(0);
		}

		.dashboard-main-wrap {
			margin-left: 0;
		}

		.dashboard-header {
			padding: 1rem;
		}

		.dashboard-content {
			padding: 1rem;
		}
	}

	@media (max-width: 767.98px) {
		.dashboard-page-title {
			font-size: 1.25rem;
		}

		.dashboard-header-right .dropdown {
			margin-left: auto;
		}
	}
	</style>

	<script>
	jQuery(document).ready(function($) {
		// Sidebar toggle for mobile
		$('.dashboard-sidebar-toggle').on('click', function() {
			$('.dashboard-sidebar-wrap').toggleClass('show');
		});

		// Close sidebar when clicking outside on mobile
		$(document).on('click', function(e) {
			if ($(window).width() <= 991.98) {
				if (!$(e.target).closest('.dashboard-sidebar-wrap, .dashboard-sidebar-toggle').length) {
					$('.dashboard-sidebar-wrap').removeClass('show');
				}
			}
		});

		// Show loading overlay for AJAX requests
		$(document).ajaxStart(function() {
			$('#houzez-ads-loading-overlay').show();
		}).ajaxStop(function() {
			$('#houzez-ads-loading-overlay').hide();
		});
	});
	</script>
</body>
</html>
