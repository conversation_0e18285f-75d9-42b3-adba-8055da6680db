<?php
/**
 * Credits and Billing Dashboard Template
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$current_user_id = get_current_user_id();
$user_credits = houzez_ads_get_user_credits( $current_user_id );

// Get credit transaction history
$credit_transactions = houzez_ads_get_user_credit_transactions( $current_user_id, 20 );

// Get credit packages
$credit_packages = houzez_ads_get_credit_packages();

// Get recent campaign spending
$recent_spending = houzez_ads_get_user_recent_spending( $current_user_id, 30 );
?>

<div class="dashboard-content-block-wrap">
	<!-- Header -->
	<div class="dashboard-content-block">
		<div class="d-flex align-items-center justify-content-between">
			<div class="dashboard-content-block-title">
				<?php _e( 'Credits & Billing', 'houzez-ads-extension' ); ?>
			</div>
			<div>
				<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#buyCreditsModal">
					<i class="houzez-icon icon-add-circle me-2"></i>
					<?php _e( 'Buy Credits', 'houzez-ads-extension' ); ?>
				</button>
			</div>
		</div>
	</div>

	<!-- Credit Balance Overview -->
	<div class="dashboard-content-block">
		<div class="row g-4">
			<div class="col-lg-3 col-md-6">
				<div class="block-wrap">
					<div class="card-body">
						<div class="stats-box bg-primary text-white rounded p-3">
							<p class="small mb-2"><?php _e( 'Available Credits', 'houzez-ads-extension' ); ?></p>
							<h2 class="mb-0"><?php echo number_format( $user_credits ); ?></h2>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-3 col-md-6">
				<div class="block-wrap">
					<div class="card-body">
						<div class="stats-box bg-success text-white rounded p-3">
							<p class="small mb-2"><?php _e( 'Credits Purchased', 'houzez-ads-extension' ); ?></p>
							<h3 class="mb-0"><?php echo number_format( $recent_spending['total_purchased'] ); ?></h3>
							<small><?php _e( 'Last 30 days', 'houzez-ads-extension' ); ?></small>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-3 col-md-6">
				<div class="block-wrap">
					<div class="card-body">
						<div class="stats-box bg-warning text-white rounded p-3">
							<p class="small mb-2"><?php _e( 'Credits Spent', 'houzez-ads-extension' ); ?></p>
							<h3 class="mb-0"><?php echo number_format( $recent_spending['total_spent'] ); ?></h3>
							<small><?php _e( 'Last 30 days', 'houzez-ads-extension' ); ?></small>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-3 col-md-6">
				<div class="block-wrap">
					<div class="card-body">
						<div class="stats-box bg-info text-white rounded p-3">
							<p class="small mb-2"><?php _e( 'Active Campaigns', 'houzez-ads-extension' ); ?></p>
							<h3 class="mb-0"><?php echo number_format( $recent_spending['active_campaigns'] ); ?></h3>
							<small><?php _e( 'Currently running', 'houzez-ads-extension' ); ?></small>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Credit Packages -->
	<div class="dashboard-content-block">
		<h5 class="mb-4"><?php _e( 'Credit Packages', 'houzez-ads-extension' ); ?></h5>
		<div class="row g-4">
			<?php foreach ( $credit_packages as $package ) : ?>
				<div class="col-lg-4 col-md-6">
					<div class="credit-package-card border rounded p-4 text-center h-100">
						<?php if ( $package['popular'] ) : ?>
							<div class="badge bg-primary mb-3"><?php _e( 'Most Popular', 'houzez-ads-extension' ); ?></div>
						<?php endif; ?>
						
						<h4 class="package-credits"><?php echo number_format( $package['credits'] ); ?></h4>
						<p class="text-muted"><?php _e( 'Credits', 'houzez-ads-extension' ); ?></p>
						
						<div class="package-price mb-3">
							<span class="price-currency">$</span>
							<span class="price-amount"><?php echo number_format( $package['price'], 2 ); ?></span>
						</div>
						
						<?php if ( $package['bonus_credits'] > 0 ) : ?>
							<div class="bonus-credits mb-3">
								<span class="badge bg-success">
									<?php printf( __( '+%d Bonus Credits', 'houzez-ads-extension' ), $package['bonus_credits'] ); ?>
								</span>
							</div>
						<?php endif; ?>
						
						<div class="package-features mb-4">
							<ul class="list-unstyled">
								<?php foreach ( $package['features'] as $feature ) : ?>
									<li><i class="houzez-icon icon-check-circle-1 text-success me-2"></i><?php echo esc_html( $feature ); ?></li>
								<?php endforeach; ?>
							</ul>
						</div>
						
						<button type="button" class="btn btn-primary w-100 buy-credits-btn" 
								data-package-id="<?php echo esc_attr( $package['id'] ); ?>"
								data-credits="<?php echo esc_attr( $package['credits'] ); ?>"
								data-price="<?php echo esc_attr( $package['price'] ); ?>">
							<?php _e( 'Purchase', 'houzez-ads-extension' ); ?>
						</button>
					</div>
				</div>
			<?php endforeach; ?>
		</div>
	</div>

	<!-- Transaction History -->
	<div class="houzez-data-content">
		<div class="dashboard-content-block">
			<h5 class="mb-4"><?php _e( 'Transaction History', 'houzez-ads-extension' ); ?></h5>
			
			<?php if ( ! empty( $credit_transactions ) ) : ?>
				<div class="table-responsive">
					<table class="table table-hover">
						<thead>
							<tr>
								<th><?php _e( 'Date', 'houzez-ads-extension' ); ?></th>
								<th><?php _e( 'Type', 'houzez-ads-extension' ); ?></th>
								<th><?php _e( 'Description', 'houzez-ads-extension' ); ?></th>
								<th><?php _e( 'Credits', 'houzez-ads-extension' ); ?></th>
								<th><?php _e( 'Balance', 'houzez-ads-extension' ); ?></th>
								<th><?php _e( 'Status', 'houzez-ads-extension' ); ?></th>
							</tr>
						</thead>
						<tbody>
							<?php foreach ( $credit_transactions as $transaction ) : ?>
								<tr>
									<td>
										<?php echo date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $transaction->created_at ) ); ?>
									</td>
									<td>
										<span class="badge <?php echo $transaction->type === 'purchase' ? 'bg-success' : 'bg-warning'; ?>">
											<?php echo $transaction->type === 'purchase' ? __( 'Purchase', 'houzez-ads-extension' ) : __( 'Campaign', 'houzez-ads-extension' ); ?>
										</span>
									</td>
									<td>
										<?php echo esc_html( $transaction->description ); ?>
										<?php if ( $transaction->campaign_id ) : ?>
											<br>
											<small class="text-muted">
												<a href="<?php echo esc_url( add_query_arg( array( 'dashboard_page' => 'campaigns', 'campaign_id' => $transaction->campaign_id ), houzez_ads_get_dashboard_url() ) ); ?>">
													<?php _e( 'View Campaign', 'houzez-ads-extension' ); ?>
												</a>
											</small>
										<?php endif; ?>
									</td>
									<td>
										<span class="<?php echo $transaction->type === 'purchase' ? 'text-success' : 'text-warning'; ?>">
											<?php echo $transaction->type === 'purchase' ? '+' : '-'; ?><?php echo number_format( abs( $transaction->credits ) ); ?>
										</span>
									</td>
									<td><?php echo number_format( $transaction->balance_after ); ?></td>
									<td>
										<span class="badge <?php echo $transaction->status === 'completed' ? 'bg-success' : 'bg-warning'; ?>">
											<?php echo ucfirst( $transaction->status ); ?>
										</span>
									</td>
								</tr>
							<?php endforeach; ?>
						</tbody>
					</table>
				</div>
				
				<div class="text-center mt-4">
					<a href="<?php echo esc_url( add_query_arg( 'view_all_transactions', '1' ) ); ?>" class="btn btn-outline-primary">
						<?php _e( 'View All Transactions', 'houzez-ads-extension' ); ?>
					</a>
				</div>
			<?php else : ?>
				<div class="text-center py-5">
					<i class="houzez-icon icon-accounting-document" style="font-size: 64px; color: #ddd;"></i>
					<h4 class="mt-3"><?php _e( 'No transactions yet', 'houzez-ads-extension' ); ?></h4>
					<p class="text-muted"><?php _e( 'Your credit purchase and campaign spending history will appear here.', 'houzez-ads-extension' ); ?></p>
				</div>
			<?php endif; ?>
		</div>
	</div>
</div>

<!-- Buy Credits Modal -->
<div class="modal fade" id="buyCreditsModal" tabindex="-1" aria-labelledby="buyCreditsModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="buyCreditsModalLabel"><?php _e( 'Purchase Credits', 'houzez-ads-extension' ); ?></h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="row g-3">
					<?php foreach ( $credit_packages as $package ) : ?>
						<div class="col-md-6">
							<div class="credit-package-option border rounded p-3 text-center">
								<h5><?php echo number_format( $package['credits'] ); ?> <?php _e( 'Credits', 'houzez-ads-extension' ); ?></h5>
								<div class="price mb-2">$<?php echo number_format( $package['price'], 2 ); ?></div>
								<?php if ( $package['bonus_credits'] > 0 ) : ?>
									<div class="bonus mb-2">
										<small class="text-success"><?php printf( __( '+%d Bonus', 'houzez-ads-extension' ), $package['bonus_credits'] ); ?></small>
									</div>
								<?php endif; ?>
								<button type="button" class="btn btn-primary btn-sm purchase-package-btn"
										data-package-id="<?php echo esc_attr( $package['id'] ); ?>">
									<?php _e( 'Select', 'houzez-ads-extension' ); ?>
								</button>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e( 'Cancel', 'houzez-ads-extension' ); ?></button>
			</div>
		</div>
	</div>
</div>

<style>
.credit-package-card {
	transition: transform 0.2s, box-shadow 0.2s;
}

.credit-package-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.package-credits {
	font-size: 2.5rem;
	font-weight: bold;
	color: #007bff;
}

.package-price {
	font-size: 1.5rem;
	font-weight: bold;
}

.price-currency {
	font-size: 1rem;
	vertical-align: top;
}

.credit-package-option {
	transition: all 0.2s;
	cursor: pointer;
}

.credit-package-option:hover {
	border-color: #007bff;
	background-color: #f8f9fa;
}

.stats-box h2, .stats-box h3 {
	font-weight: bold;
}
</style>

<script>
jQuery(document).ready(function($) {
	// Handle credit package purchase
	$('.purchase-package-btn, .buy-credits-btn').on('click', function(e) {
		e.preventDefault();
		
		var packageId = $(this).data('package-id');
		var credits = $(this).data('credits');
		var price = $(this).data('price');
		
		// Close modal if open
		$('#buyCreditsModal').modal('hide');
		
		// Redirect to WooCommerce checkout or payment processor
		if (typeof houzez_ads_ajax !== 'undefined') {
			$.ajax({
				url: houzez_ads_ajax.ajax_url,
				type: 'POST',
				data: {
					action: 'houzez_ads_purchase_credits',
					package_id: packageId,
					nonce: houzez_ads_ajax.nonce
				},
				success: function(response) {
					if (response.success && response.data.redirect_url) {
						window.location.href = response.data.redirect_url;
					} else {
						alert(response.data.message || '<?php _e( "Error processing request", "houzez-ads-extension" ); ?>');
					}
				},
				error: function() {
					alert('<?php _e( "Error processing request", "houzez-ads-extension" ); ?>');
				}
			});
		}
	});
});
</script>
