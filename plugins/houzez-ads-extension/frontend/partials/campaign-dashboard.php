<?php
/**
 * Campaign dashboard template using Houzez dashboard UI
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$current_user_id = get_current_user_id();

// Check if we should show the create form
$show_create_form = isset( $_GET['create_campaign'] ) && $_GET['create_campaign'] == '1';

if ( $show_create_form ) {
	// Show the upload form instead of dashboard
	echo do_shortcode( '[houzez_ads_upload]' );
	return;
}

// Get campaign status filter
$status_filter = isset( $_GET['campaign_status'] ) ? sanitize_text_field( $_GET['campaign_status'] ) : 'all';

// Build query args based on filter
$query_args = array(
	'post_type' => 'banner_campaign',
	'author' => $current_user_id,
	'posts_per_page' => -1,
	'orderby' => 'date',
	'order' => 'DESC'
);

if ( $status_filter !== 'all' ) {
	$query_args['meta_query'] = array(
		array(
			'key' => 'campaign_status',
			'value' => $status_filter,
			'compare' => '='
		)
	);
} else {
	$query_args['post_status'] = array( 'publish', 'draft', 'pending' );
}

$campaigns = get_posts( $query_args );
$statuses = houzez_ads_get_campaign_statuses();

// Get campaign counts for tabs
$all_campaigns = get_posts( array(
	'post_type' => 'banner_campaign',
	'author' => $current_user_id,
	'posts_per_page' => -1,
	'post_status' => array( 'publish', 'draft', 'pending' ),
	'fields' => 'ids'
) );

$status_counts = array();
foreach ( $statuses as $status_key => $status_label ) {
	$status_counts[ $status_key ] = 0;
}

foreach ( $all_campaigns as $campaign_id ) {
	$campaign_status = get_post_meta( $campaign_id, 'campaign_status', true );
	if ( isset( $status_counts[ $campaign_status ] ) ) {
		$status_counts[ $campaign_status ]++;
	}
}

$total_count = count( $all_campaigns );
?>

<div class="dashboard-content-block-wrap">
	<div class="dashboard-content-block">
		<div class="d-flex align-items-center justify-content-between">
			<div class="dashboard-content-block-title">
				<?php _e( 'My Ad Campaigns', 'houzez-ads-extension' ); ?>
			</div>
			<div>
				<a href="<?php echo esc_url( add_query_arg( 'create_campaign', '1' ) ); ?>" class="btn btn-primary">
					<i class="houzez-icon icon-add-circle me-2"></i>
					<?php _e( 'Create New Campaign', 'houzez-ads-extension' ); ?>
				</a>
			</div>
		</div>
	</div>

	<!-- Campaign Status Tabs (similar to Houzez property tabs) -->
	<div class="dashboard-content-block">
		<div class="dashboard-tabs-wrap">
			<ul class="nav nav-tabs" role="tablist">
				<li class="nav-item">
					<a class="nav-link <?php echo $status_filter === 'all' ? 'active' : ''; ?>"
					   href="<?php echo esc_url( remove_query_arg( 'campaign_status' ) ); ?>">
						<?php _e( 'All', 'houzez-ads-extension' ); ?>
						<span class="badge bg-secondary ms-1"><?php echo $total_count; ?></span>
					</a>
				</li>
				<?php foreach ( $statuses as $status_key => $status_label ) : ?>
					<li class="nav-item">
						<a class="nav-link <?php echo $status_filter === $status_key ? 'active' : ''; ?>"
						   href="<?php echo esc_url( add_query_arg( 'campaign_status', $status_key ) ); ?>">
							<?php echo esc_html( $status_label ); ?>
							<span class="badge bg-secondary ms-1"><?php echo $status_counts[ $status_key ]; ?></span>
						</a>
					</li>
				<?php endforeach; ?>
			</ul>
		</div>
	</div>

	<?php if ( ! empty( $campaigns ) ) : ?>
		<div class="houzez-data-content">
			<!-- Filters similar to Houzez property filters -->
			<div class="houzez-table-filters">
				<div class="row">
					<div class="col-md-4">
						<div class="dashboard-search-filter">
							<span><i class="houzez-icon icon-search-1"></i></span>
							<input type="text" id="campaign-search" class="form-control dashboard-search" placeholder="<?php _e( 'Search campaigns...', 'houzez-ads-extension' ); ?>">
						</div>
					</div>
					<div class="col-md-4">
						<select id="campaign-zone-filter" class="selectpicker form-control" title="<?php _e( 'Filter by Zone', 'houzez-ads-extension' ); ?>">
							<option value=""><?php _e( 'All Zones', 'houzez-ads-extension' ); ?></option>
							<?php
							$zones = houzez_ads_get_available_zones();
							foreach ( $zones as $key => $label ) : ?>
								<option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
							<?php endforeach; ?>
						</select>
					</div>
					<div class="col-md-4">
						<select id="campaign-type-filter" class="selectpicker form-control" title="<?php _e( 'Filter by Type', 'houzez-ads-extension' ); ?>">
							<option value=""><?php _e( 'All Types', 'houzez-ads-extension' ); ?></option>
							<?php
							$ad_types = houzez_ads_get_available_ad_types();
							foreach ( $ad_types as $key => $label ) : ?>
								<option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
							<?php endforeach; ?>
						</select>
					</div>
				</div>
			</div>

			<!-- Bulk Actions Bar -->
			<div class="bulk-actions-bar d-none">
				<div class="d-flex align-items-center justify-content-between p-3 bg-light border rounded">
					<div class="bulk-actions-left">
						<span class="selected-count">0</span> <?php _e( 'campaigns selected', 'houzez-ads-extension' ); ?>
					</div>
					<div class="bulk-actions-right">
						<select class="form-select form-select-sm me-2" id="bulk-action-select">
							<option value=""><?php _e( 'Bulk Actions', 'houzez-ads-extension' ); ?></option>
							<option value="delete"><?php _e( 'Delete', 'houzez-ads-extension' ); ?></option>
							<option value="pause"><?php _e( 'Pause', 'houzez-ads-extension' ); ?></option>
							<option value="resume"><?php _e( 'Resume', 'houzez-ads-extension' ); ?></option>
						</select>
						<button type="button" class="btn btn-sm btn-primary" id="apply-bulk-action">
							<?php _e( 'Apply', 'houzez-ads-extension' ); ?>
						</button>
						<button type="button" class="btn btn-sm btn-secondary" id="cancel-bulk-selection">
							<?php _e( 'Cancel', 'houzez-ads-extension' ); ?>
						</button>
					</div>
				</div>
			</div>

			<div class="table-responsive">
				<table class="table table-hover campaign-table">
					<thead>
						<tr>
							<th width="40">
								<label class="control control--checkbox">
									<input type="checkbox" id="select-all-campaigns" class="control control--checkbox">
									<span class="control__indicator"></span>
								</label>
							</th>
							<th width="100"><?php _e( 'Thumbnail', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Campaign', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Type', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Zone', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Duration', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Status', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Performance', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Credits', 'houzez-ads-extension' ); ?></th>
							<th width="80"><?php _e( 'Actions', 'houzez-ads-extension' ); ?></th>
						</tr>
					</thead>
					<tbody>
						<?php foreach ( $campaigns as $campaign_post ) :
							// Set global variable for campaign item template
							$GLOBALS['campaign_post'] = $campaign_post;
							include HOUZEZ_ADS_EXTENSION_PATH . 'frontend/partials/campaign-item.php';
						endforeach; ?>
					</tbody>
				</table>
			</div>
		</div>
	<?php else : ?>
		<div class="dashboard-content-block">
			<div class="text-center py-5">
				<i class="houzez-icon icon-megaphone" style="font-size: 64px; color: #ddd;"></i>
				<h4 class="mt-3"><?php _e( 'No campaigns yet', 'houzez-ads-extension' ); ?></h4>
				<p class="text-muted"><?php _e( 'Create your first ad campaign to start promoting your properties and services.', 'houzez-ads-extension' ); ?></p>
				<a href="<?php echo esc_url( add_query_arg( 'create_campaign', '1' ) ); ?>" class="btn btn-primary">
					<i class="houzez-icon icon-add-circle me-2"></i>
					<?php _e( 'Create Your First Campaign', 'houzez-ads-extension' ); ?>
				</a>
			</div>
		</div>
	<?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
	// Search functionality
	$('#campaign-search').on('keyup', function() {
		var value = $(this).val().toLowerCase();
		$('.campaign-table tbody tr').filter(function() {
			$(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
		});
		updateVisibleCount();
	});

	// Zone filter
	$('#campaign-zone-filter').on('change', function() {
		var zone = $(this).val();
		filterCampaigns();
	});

	// Type filter
	$('#campaign-type-filter').on('change', function() {
		var type = $(this).val();
		filterCampaigns();
	});

	// Filter campaigns based on selected filters
	function filterCampaigns() {
		var zone = $('#campaign-zone-filter').val();
		var type = $('#campaign-type-filter').val();
		var search = $('#campaign-search').val().toLowerCase();

		$('.campaign-table tbody tr').each(function() {
			var $row = $(this);
			var rowZone = $row.data('zone');
			var rowType = $row.data('type');
			var rowText = $row.text().toLowerCase();

			var showRow = true;

			if (zone && rowZone !== zone) showRow = false;
			if (type && rowType !== type) showRow = false;
			if (search && rowText.indexOf(search) === -1) showRow = false;

			$row.toggle(showRow);
		});

		updateVisibleCount();
	}

	// Update visible campaign count
	function updateVisibleCount() {
		var visibleCount = $('.campaign-table tbody tr:visible').length;
		$('.visible-count').text(visibleCount);
	}

	// Select all campaigns
	$('#select-all-campaigns').on('change', function() {
		var isChecked = $(this).is(':checked');
		$('.campaign-bulk-delete:visible').prop('checked', isChecked);
		updateBulkActions();
	});

	// Individual campaign selection
	$(document).on('change', '.campaign-bulk-delete', function() {
		updateBulkActions();

		// Update select all checkbox
		var totalVisible = $('.campaign-bulk-delete:visible').length;
		var selectedVisible = $('.campaign-bulk-delete:visible:checked').length;
		$('#select-all-campaigns').prop('checked', totalVisible > 0 && selectedVisible === totalVisible);
	});

	// Update bulk actions bar
	function updateBulkActions() {
		var selectedCount = $('.campaign-bulk-delete:checked').length;
		$('.selected-count').text(selectedCount);

		if (selectedCount > 0) {
			$('.bulk-actions-bar').removeClass('d-none');
		} else {
			$('.bulk-actions-bar').addClass('d-none');
		}
	}

	// Cancel bulk selection
	$('#cancel-bulk-selection').on('click', function() {
		$('.campaign-bulk-delete').prop('checked', false);
		$('#select-all-campaigns').prop('checked', false);
		updateBulkActions();
	});

	// Apply bulk action
	$('#apply-bulk-action').on('click', function() {
		var action = $('#bulk-action-select').val();
		var selectedCampaigns = [];

		$('.campaign-bulk-delete:checked').each(function() {
			selectedCampaigns.push($(this).val());
		});

		if (!action || selectedCampaigns.length === 0) {
			alert('<?php _e( "Please select an action and campaigns", "houzez-ads-extension" ); ?>');
			return;
		}

		var confirmMessage = '';
		switch (action) {
			case 'delete':
				confirmMessage = '<?php _e( "Are you sure you want to delete the selected campaigns?", "houzez-ads-extension" ); ?>';
				break;
			case 'pause':
				confirmMessage = '<?php _e( "Are you sure you want to pause the selected campaigns?", "houzez-ads-extension" ); ?>';
				break;
			case 'resume':
				confirmMessage = '<?php _e( "Are you sure you want to resume the selected campaigns?", "houzez-ads-extension" ); ?>';
				break;
		}

		if (confirm(confirmMessage)) {
			$.ajax({
				url: houzez_ads_ajax.ajax_url,
				type: 'POST',
				data: {
					action: 'houzez_ads_bulk_action',
					bulk_action: action,
					campaign_ids: selectedCampaigns,
					nonce: '<?php echo wp_create_nonce( "houzez_ads_bulk_action" ); ?>'
				},
				beforeSend: function() {
					$('#apply-bulk-action').prop('disabled', true).text('<?php _e( "Processing...", "houzez-ads-extension" ); ?>');
				},
				success: function(response) {
					if (response.success) {
						location.reload();
					} else {
						alert(response.data.message || '<?php _e( "Failed to perform bulk action", "houzez-ads-extension" ); ?>');
					}
				},
				error: function() {
					alert('<?php _e( "An error occurred while performing bulk action", "houzez-ads-extension" ); ?>');
				},
				complete: function() {
					$('#apply-bulk-action').prop('disabled', false).text('<?php _e( "Apply", "houzez-ads-extension" ); ?>');
				}
			});
		}
	});

	// Delete single campaign
	$(document).on('click', '.delete-campaign', function(e) {
		e.preventDefault();

		var campaignId = $(this).data('campaign-id');
		var campaignTitle = $(this).data('campaign-title');
		var $row = $(this).closest('tr');

		if (confirm('<?php _e( "Are you sure you want to delete the campaign", "houzez-ads-extension" ); ?> "' + campaignTitle + '"?')) {
			$.ajax({
				url: houzez_ads_ajax.ajax_url,
				type: 'POST',
				data: {
					action: 'houzez_ads_delete_campaign',
					campaign_id: campaignId,
					nonce: $(this).data('nonce')
				},
				beforeSend: function() {
					$row.css('opacity', '0.5');
				},
				success: function(response) {
					if (response.success) {
						$row.fadeOut(300, function() {
							$(this).remove();
							updateVisibleCount();
							// Check if no campaigns left
							if ($('.campaign-table tbody tr').length === 0) {
								location.reload();
							}
						});
					} else {
						alert(response.data.message || '<?php _e( "Failed to delete campaign", "houzez-ads-extension" ); ?>');
						$row.css('opacity', '1');
					}
				},
				error: function() {
					alert('<?php _e( "An error occurred while deleting the campaign", "houzez-ads-extension" ); ?>');
					$row.css('opacity', '1');
				}
			});
		}
	});

	// Duplicate campaign
	$(document).on('click', '.duplicate-campaign', function(e) {
		e.preventDefault();

		var campaignId = $(this).data('campaign-id');
		var $button = $(this);

		$.ajax({
			url: houzez_ads_ajax.ajax_url,
			type: 'POST',
			data: {
				action: 'houzez_ads_duplicate_campaign',
				campaign_id: campaignId,
				nonce: $(this).data('nonce')
			},
			beforeSend: function() {
				$button.text('<?php _e( "Duplicating...", "houzez-ads-extension" ); ?>');
			},
			success: function(response) {
				if (response.success) {
					location.reload();
				} else {
					alert(response.data.message || '<?php _e( "Failed to duplicate campaign", "houzez-ads-extension" ); ?>');
				}
			},
			error: function() {
				alert('<?php _e( "An error occurred while duplicating the campaign", "houzez-ads-extension" ); ?>');
			},
			complete: function() {
				$button.text('<?php _e( "Duplicate", "houzez-ads-extension" ); ?>');
			}
		});
	});

	// Pause/Resume campaign
	$(document).on('click', '.pause-campaign, .resume-campaign', function(e) {
		e.preventDefault();

		var campaignId = $(this).data('campaign-id');
		var action = $(this).hasClass('pause-campaign') ? 'pause' : 'resume';
		var $button = $(this);

		$.ajax({
			url: houzez_ads_ajax.ajax_url,
			type: 'POST',
			data: {
				action: 'houzez_ads_toggle_campaign',
				campaign_id: campaignId,
				toggle_action: action,
				nonce: $(this).data('nonce')
			},
			beforeSend: function() {
				$button.text(action === 'pause' ? '<?php _e( "Pausing...", "houzez-ads-extension" ); ?>' : '<?php _e( "Resuming...", "houzez-ads-extension" ); ?>');
			},
			success: function(response) {
				if (response.success) {
					location.reload();
				} else {
					alert(response.data.message || '<?php _e( "Failed to update campaign", "houzez-ads-extension" ); ?>');
				}
			},
			error: function() {
				alert('<?php _e( "An error occurred while updating the campaign", "houzez-ads-extension" ); ?>');
			}
		});
	});

	// Initialize tooltips
	if (typeof bootstrap !== 'undefined') {
		var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
		var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
			return new bootstrap.Tooltip(tooltipTriggerEl);
		});
	}

	// Initialize selectpicker if available
	if ($.fn.selectpicker) {
		$('.selectpicker').selectpicker({
			style: 'btn-outline-secondary',
			size: 4
		});
	}

	// Initial count update
	updateVisibleCount();
});
</script>
