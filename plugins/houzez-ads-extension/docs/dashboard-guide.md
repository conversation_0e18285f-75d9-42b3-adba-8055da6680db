# Houzez Ads Extension Dashboard Guide

## Overview

The Houzez Ads Extension provides a comprehensive dashboard interface that seamlessly integrates with the existing Houzez theme dashboard. This guide explains how to use the dashboard features and available shortcodes.

## Dashboard Features

### 1. Campaign Management Dashboard

The main dashboard provides a complete interface for managing ad campaigns, similar to the Houzez property management interface.

**Access:** Navigate to Dashboard → Ad Campaigns

**Features:**
- Campaign listing with filtering and search
- Bulk actions (delete, pause, resume)
- Status-based tabs (All, Approved, Pending, Rejected, Expired)
- Campaign analytics and performance metrics
- Quick actions (edit, duplicate, view stats)

**Shortcode:** `[houzez_ads_dashboard]`

### 2. Campaign Insights Dashboard

Provides detailed analytics and performance data for ad campaigns.

**Access:** Dashboard → Campaign Insights

**Features:**
- Performance overview with charts
- Campaign statistics (impressions, clicks, CTR)
- Top performing campaigns
- Zone and type performance analysis
- Date filtering and campaign-specific insights

**Shortcode:** `[houzez_ads_insights]`

**Parameters:**
- `user_id` - Specific user ID (defaults to current user)

### 3. Credits & Billing Dashboard

Manages user credits and billing information.

**Access:** Dashboard → Credits & Billing

**Features:**
- Credit balance overview
- Purchase history and transactions
- Credit packages with pricing
- Recent spending analytics
- WooCommerce integration for payments

**Shortcode:** `[houzez_ads_credits]`

**Parameters:**
- `user_id` - Specific user ID (defaults to current user)

## Available Shortcodes

### Dashboard Shortcodes

```php
// Main campaign dashboard
[houzez_ads_dashboard]

// Campaign insights and analytics
[houzez_ads_insights]

// Credits and billing management
[houzez_ads_credits]

// Campaign upload/creation form
[houzez_ads_upload]

// Pricing table display
[houzez_ads_pricing_table show_title="true" columns="3"]
```

### Ad Display Shortcodes

```php
// Display ads in specific zones
[houzez_ads_zone zone="homepage"]
[houzez_ads_zone zone="sidebar"]
[houzez_ads_zone zone="search"]
[houzez_ads_zone zone="property_detail"]

// Agency profile ads
[houzez_ads_homepage_agencies limit="3"]
[houzez_ads_sidebar_agencies limit="5"]

// Business partnership ads
[houzez_ads_business_partnership zone="property_detail"]
```

## Dashboard Integration

### Menu Integration

The dashboard automatically integrates with the Houzez dashboard menu system:

- **Ad Campaigns** - Main campaign management
- **Create Campaign** - Campaign creation form
- **Campaign Insights** - Analytics and reporting
- **Credits & Billing** - Credit management

### User Permissions

Dashboard access is controlled by user roles and capabilities:

```php
// Check if user can access campaigns
if ( houzez_ads_user_can_create_campaigns() ) {
    // Show dashboard features
}

// Default allowed roles
$allowed_roles = array(
    'administrator',
    'houzez_agency',
    'houzez_agent',
    'houzez_manager'
);
```

### Customization

#### Filter Hooks

```php
// Customize allowed user roles
add_filter( 'houzez_ads_allowed_roles', function( $roles ) {
    $roles[] = 'custom_role';
    return $roles;
});

// Customize credit packages
add_filter( 'houzez_ads_credit_packages', function( $packages ) {
    // Modify packages array
    return $packages;
});

// Customize campaign price calculation
add_filter( 'houzez_ads_calculated_price', function( $price, $zone, $duration, $quantity, $ad_type ) {
    // Custom pricing logic
    return $price;
}, 10, 5);
```

#### Action Hooks

```php
// Before dashboard content
add_action( 'houzez_ads_before_dashboard_content', function() {
    // Custom content
});

// After dashboard content
add_action( 'houzez_ads_after_dashboard_content', function() {
    // Custom content
});

// Campaign creation
add_action( 'houzez_ads_campaign_created', function( $campaign_id ) {
    // Handle new campaign
});
```

## Styling and Customization

### CSS Classes

The dashboard uses consistent CSS classes that match Houzez theme patterns:

```css
/* Main dashboard containers */
.dashboard-content-block-wrap
.dashboard-content-block
.dashboard-content-block-title

/* Navigation and tabs */
.dashboard-tabs-wrap
.nav-tabs
.nav-link

/* Tables and data */
.houzez-data-content
.campaign-table
.houzez-table-filters

/* Campaign items */
.campaign-title
.campaign-meta
.campaign-analytics
.analytics-item

/* Controls and actions */
.bulk-actions-bar
.control
.action-btn
```

### Theme Integration

The dashboard automatically adapts to Houzez theme styling:

- Uses Houzez color variables
- Matches existing dashboard layout
- Responsive design patterns
- Icon consistency

## Analytics and Reporting

### Available Metrics

- **Impressions** - Number of times ads were displayed
- **Clicks** - Number of clicks on ads
- **CTR** - Click-through rate percentage
- **Campaign Performance** - Individual campaign statistics
- **Zone Performance** - Performance by ad placement
- **Type Performance** - Performance by campaign type

### Data Export

Analytics data can be accessed programmatically:

```php
// Get user analytics
$analytics = houzez_ads_get_user_analytics( $user_id, $start_date, $end_date, $campaign_id );

// Get campaign counts
$counts = houzez_ads_get_user_campaign_counts( $user_id );

// Get credit transactions
$transactions = houzez_ads_get_user_credit_transactions( $user_id, $limit );
```

## Best Practices

### Performance

1. **Caching** - Analytics data is cached for performance
2. **Pagination** - Large datasets are paginated
3. **Lazy Loading** - Charts and heavy content load on demand

### User Experience

1. **Consistent UI** - Matches Houzez dashboard patterns
2. **Responsive Design** - Works on all device sizes
3. **Progressive Enhancement** - Core functionality works without JavaScript

### Security

1. **Capability Checks** - All actions verify user permissions
2. **Nonce Verification** - AJAX requests use nonces
3. **Data Sanitization** - All input is properly sanitized

## Troubleshooting

### Common Issues

1. **Dashboard not showing** - Check user permissions and role configuration
2. **Analytics not loading** - Verify database tables exist and have data
3. **Styling issues** - Ensure Houzez theme is active and CSS is loaded

### Debug Mode

Enable debug mode for detailed logging:

```php
define( 'HOUZEZ_ADS_DEBUG', true );
```

### Support

For additional support and customization:

- Check plugin documentation
- Review code comments
- Contact plugin support team

## Version Compatibility

- **WordPress:** 5.0+
- **Houzez Theme:** 2.0+
- **PHP:** 7.4+
- **MySQL:** 5.6+

## Changelog

### Version 1.0.0
- Initial dashboard implementation
- Campaign management interface
- Analytics and insights dashboard
- Credits and billing system
- Houzez theme integration
