# Houzez Ads Extension - Dashboard Guide

## Overview

The Houzez Ads Extension now features a fully integrated dashboard system that seamlessly integrates with the existing Houzez theme dashboard. This guide covers the new template-based architecture and how to set up and use the dashboard system.

## Architecture

### Template-Based System

The dashboard has been converted from a shortcode-based system to a WordPress page template system that replicates the exact Houzez dashboard structure:

- **Main Template**: `templates/user_dashboard_ads.php`
- **Template Parts**: Located in `template-parts/ads-dashboard/`
- **Integration**: Seamlessly integrates with existing Houzez dashboard navigation

### Key Components

1. **Dashboard Overview** - Campaign summary and quick stats
2. **Campaign Management** - Create, edit, and manage campaigns
3. **Insights & Analytics** - Performance tracking and reporting
4. **Credits & Billing** - Credit management and purchase system

## Setup Instructions

### 1. Create Dashboard Page

1. Go to **Pages > Add New** in WordPress admin
2. Set the page title to "Ad Campaign Dashboard" (or your preferred name)
3. In the **Page Attributes** section, select **Template**: `Ads Dashboard`
4. Publish the page
5. Note the page URL for user access

### 2. Configure User Permissions

The dashboard automatically checks user permissions using the existing `houzez_ads_user_can_create_campaigns()` function. Ensure your user roles are properly configured in the plugin settings.

### 3. Menu Integration

The dashboard automatically integrates with the Houzez sidebar menu. When users access any dashboard page, they will see an "Ad Campaigns" section added to the navigation menu.

## Dashboard Sections

### Overview Dashboard

**URL**: `?ads_page=overview`

Features:
- Campaign statistics overview
- Quick action buttons
- Recent campaigns list
- Performance chart (last 30 days)

**Template**: `template-parts/ads-dashboard/overview.php`

### Campaign Management

**URL**: `?ads_page=campaigns`

Features:
- Campaign listing with status tabs
- Advanced filtering and search
- Bulk actions (delete, pause, resume)
- Campaign status management

**Templates**:
- Main: `template-parts/ads-dashboard/campaigns/main.php`
- Tabs: `template-parts/ads-dashboard/campaigns/tabs.php`
- Filters: `template-parts/ads-dashboard/campaigns/filters.php`
- Item: `template-parts/ads-dashboard/campaigns/campaign-item.php`

### Campaign Creation

**URL**: `?ads_page=create_campaign`

Features:
- Credit check workflow
- Real-time cost calculation
- Form validation
- Image upload and preview

**Template**: `template-parts/ads-dashboard/campaigns/create.php`

### Insights & Analytics

**URL**: `?ads_page=insights`

Features:
- Performance metrics
- Interactive charts
- Date range filtering
- Campaign comparison

**Template**: `template-parts/ads-dashboard/insights/main.php`

### Credits & Billing

**URL**: `?ads_page=credits`

Features:
- Credit balance overview
- Package purchasing
- Transaction history
- Billing management

**Template**: `template-parts/ads-dashboard/credits/main.php`

## Credit Check Workflow

The dashboard implements a comprehensive credit check system similar to Houzez property creation:

1. **Pre-Creation Check**: Users must have sufficient credits before accessing campaign creation
2. **Real-Time Validation**: Cost calculation updates as users select options
3. **Insufficient Credits**: Automatic redirect to credit purchase page
4. **Purchase Integration**: Seamless credit purchase workflow

## Template Customization

### Override Templates

You can override any template by copying it to your theme:

```
/themes/your-theme/houzez-ads-extension/template-parts/ads-dashboard/overview.php
```

### Template Hierarchy

1. Child theme directory
2. Parent theme directory  
3. Plugin directory (fallback)

### Available Template Functions

```php
// Load template part
houzez_ads_get_template_part('template-parts/ads-dashboard/overview');

// Get template content
$content = houzez_ads_get_template('template-parts/ads-dashboard/overview');

// Check if dashboard page
if (houzez_ads_is_dashboard_page()) {
    // Dashboard-specific code
}

// Get dashboard URL
$url = houzez_ads_get_dashboard_url('campaigns', array('status' => 'approved'));
```

## JavaScript Integration

### Dashboard Objects

The dashboard includes several JavaScript objects for enhanced functionality:

```javascript
// Main dashboard functionality
houzezAdsDashboard.init();

// Campaign management
houzezAdsCampaigns.init();

// Campaign creation
houzezAdsCampaignCreation.init();
```

### AJAX Endpoints

All dashboard actions use AJAX for seamless user experience:

- `houzez_ads_delete_campaign`
- `houzez_ads_duplicate_campaign`
- `houzez_ads_toggle_campaign`
- `houzez_ads_bulk_action`
- `houzez_ads_calculate_cost`
- `houzez_ads_purchase_credits`

## Styling

### CSS Classes

The dashboard uses Houzez-compatible CSS classes:

- `.dashboard-content-block-wrap`
- `.dashboard-content-block`
- `.dashboard-statistic-block`
- `.dashboard-table-wrap`
- `.dashboard-empty-state`

### Custom Styling

Add custom styles in your theme's CSS:

```css
.houzez-ads-nav-box {
    border-top: 1px solid #e9ecef;
    margin-top: 1rem;
    padding-top: 1rem;
}

.credit-package-card .membership-package:hover {
    border-color: #007bff;
    transform: translateY(-5px);
}
```

## User Experience Flow

### New User Journey

1. User logs into Houzez dashboard
2. Sees "Ad Campaigns" menu section
3. Clicks "Campaign Overview" to see dashboard
4. If no credits, prompted to purchase
5. Creates first campaign through guided workflow

### Existing User Journey

1. Access dashboard from Houzez menu
2. View campaign performance in overview
3. Manage campaigns through dedicated section
4. Monitor analytics and insights
5. Purchase additional credits as needed

## Migration from Shortcodes

If you were previously using shortcode-based dashboard:

1. Create new dashboard page with template
2. Update any custom links to use new URL structure
3. Test all functionality with different user roles
4. Remove old shortcode-based pages

## Troubleshooting

### Common Issues

**Dashboard not showing**: Ensure the page template is set correctly and user has proper permissions.

**Menu not appearing**: Check that `houzez_ads_user_can_create_campaigns()` returns true for the user.

**Credit check failing**: Verify credit calculation functions are working and database tables exist.

**Templates not loading**: Check file permissions and template hierarchy.

### Debug Mode

Enable debug mode in the plugin settings to see detailed error messages and template loading information.

## Security Considerations

- All dashboard actions require proper nonces
- User permissions checked on every request
- File uploads validated and sanitized
- SQL queries use prepared statements
- AJAX endpoints verify user capabilities

## Performance Optimization

- Dashboard data is cached where appropriate
- Images are optimized during upload
- Analytics queries are optimized for large datasets
- Pagination implemented for large campaign lists

## Browser Compatibility

The dashboard is compatible with:
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## Support

For technical support or customization requests, please refer to the plugin documentation or contact support through the appropriate channels.
